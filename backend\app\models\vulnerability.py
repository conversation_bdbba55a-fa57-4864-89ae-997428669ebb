from pydantic import BaseModel
from typing import Optional, List
from datetime import datetime
from enum import Enum

class SeverityLevel(str, Enum):
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    INFO = "info"

class VulnerabilityStatus(str, Enum):
    OPEN = "open"
    FIXED = "fixed"
    ACCEPTED = "accepted"
    IGNORED = "ignored"

class Vulnerability(BaseModel):
    id: str
    name: str
    description: Optional[str] = None
    severity: SeverityLevel
    status: VulnerabilityStatus
    target_url: str
    target_name: Optional[str] = None
    discovered_at: datetime
    last_seen: Optional[datetime] = None
    cvss_score: Optional[float] = None
    cve_id: Optional[str] = None
    category: Optional[str] = None
    recommendation: Optional[str] = None
    http_request: Optional[str] = None  # HTTP请求信息
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class VulnerabilityResponse(BaseModel):
    vulnerabilities: List[Vulnerability]
    total_count: int
    critical_count: int
    high_count: int
    
class VulnerabilityFilter(BaseModel):
    severity_levels: Optional[List[SeverityLevel]] = [SeverityLevel.CRITICAL, SeverityLevel.HIGH]
    status: Optional[List[VulnerabilityStatus]] = [VulnerabilityStatus.OPEN]
    target_url: Optional[str] = None
    limit: Optional[int] = 100
    offset: Optional[int] = 0
