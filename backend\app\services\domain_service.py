"""
域名处理状态服务
"""
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_
from typing import List, Dict, Optional
from datetime import datetime
import logging

from app.models.db_models import DomainProcessingStatus
from app.database import AsyncSessionLocal

logger = logging.getLogger(__name__)

class DomainService:
    """域名处理状态服务类"""
    
    async def get_session(self) -> AsyncSession:
        """获取数据库会话"""
        return AsyncSessionLocal()
    
    async def save_domain_status(
        self,
        domain: str,
        is_processed: bool,
        domain_type: str,
        parent_domain: Optional[str] = None
    ) -> bool:
        """
        保存域名处理状态
        
        Args:
            domain: 域名
            is_processed: 是否已处理
            domain_type: 域名类型 ('main_domain' 或 'subdomain')
            parent_domain: 父域名（仅对子域名有效）
        """
        async with await self.get_session() as session:
            try:
                # 检查是否已存在
                existing = await session.get(DomainProcessingStatus, domain)
                
                if existing:
                    # 更新现有记录
                    existing.is_processed = is_processed
                    existing.domain_type = domain_type
                    existing.parent_domain = parent_domain
                    existing.updated_at = datetime.utcnow()
                else:
                    # 创建新记录
                    domain_status = DomainProcessingStatus(
                        domain=domain,
                        is_processed=is_processed,
                        domain_type=domain_type,
                        parent_domain=parent_domain
                    )
                    session.add(domain_status)
                
                await session.commit()
                logger.info(f"Saved domain status: {domain} -> {is_processed}")
                return True
                
            except Exception as e:
                await session.rollback()
                logger.error(f"Error saving domain status for {domain}: {str(e)}")
                raise
    
    async def get_domain_status(self, domain: str) -> Optional[bool]:
        """获取域名处理状态"""
        async with await self.get_session() as session:
            try:
                domain_status = await session.get(DomainProcessingStatus, domain)
                return domain_status.is_processed if domain_status else None
                
            except Exception as e:
                logger.error(f"Error getting domain status for {domain}: {str(e)}")
                return None
    
    async def get_all_domain_statuses(self) -> Dict[str, bool]:
        """获取所有域名的处理状态"""
        async with await self.get_session() as session:
            try:
                query = select(DomainProcessingStatus.domain, DomainProcessingStatus.is_processed)
                result = await session.execute(query)
                
                return dict(result.all())
                
            except Exception as e:
                logger.error(f"Error getting all domain statuses: {str(e)}")
                return {}
    
    async def get_processed_domains(self, domain_type: Optional[str] = None) -> List[str]:
        """获取已处理的域名列表"""
        async with await self.get_session() as session:
            try:
                query = select(DomainProcessingStatus.domain).where(
                    DomainProcessingStatus.is_processed == True
                )
                
                if domain_type:
                    query = query.where(DomainProcessingStatus.domain_type == domain_type)
                
                result = await session.execute(query)
                return [row[0] for row in result.all()]
                
            except Exception as e:
                logger.error(f"Error getting processed domains: {str(e)}")
                return []
    
    async def get_unprocessed_domains(self, domain_type: Optional[str] = None) -> List[str]:
        """获取未处理的域名列表"""
        async with await self.get_session() as session:
            try:
                query = select(DomainProcessingStatus.domain).where(
                    DomainProcessingStatus.is_processed == False
                )
                
                if domain_type:
                    query = query.where(DomainProcessingStatus.domain_type == domain_type)
                
                result = await session.execute(query)
                return [row[0] for row in result.all()]
                
            except Exception as e:
                logger.error(f"Error getting unprocessed domains: {str(e)}")
                return []
    
    async def batch_save_domain_statuses(self, domain_statuses: Dict[str, Dict]) -> bool:
        """
        批量保存域名处理状态
        
        Args:
            domain_statuses: {domain: {"is_processed": bool, "domain_type": str, "parent_domain": str}}
        """
        async with await self.get_session() as session:
            try:
                for domain, status_info in domain_statuses.items():
                    # 检查是否已存在
                    existing = await session.get(DomainProcessingStatus, domain)
                    
                    if existing:
                        # 更新现有记录
                        existing.is_processed = status_info["is_processed"]
                        existing.domain_type = status_info["domain_type"]
                        existing.parent_domain = status_info.get("parent_domain")
                        existing.updated_at = datetime.utcnow()
                    else:
                        # 创建新记录
                        domain_status = DomainProcessingStatus(
                            domain=domain,
                            is_processed=status_info["is_processed"],
                            domain_type=status_info["domain_type"],
                            parent_domain=status_info.get("parent_domain")
                        )
                        session.add(domain_status)
                
                await session.commit()
                logger.info(f"Batch saved {len(domain_statuses)} domain statuses")
                return True
                
            except Exception as e:
                await session.rollback()
                logger.error(f"Error batch saving domain statuses: {str(e)}")
                raise
    
    async def delete_domain_status(self, domain: str) -> bool:
        """删除域名处理状态"""
        async with await self.get_session() as session:
            try:
                domain_status = await session.get(DomainProcessingStatus, domain)
                if domain_status:
                    await session.delete(domain_status)
                    await session.commit()
                    logger.info(f"Deleted domain status: {domain}")
                    return True
                return False
                
            except Exception as e:
                await session.rollback()
                logger.error(f"Error deleting domain status for {domain}: {str(e)}")
                raise

# 创建全局实例
domain_service = DomainService()
