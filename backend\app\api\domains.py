"""
域名处理状态API
"""
from fastapi import APIRouter, HTTPException
from typing import Dict, List, Optional
from pydantic import BaseModel
import logging

from app.services.domain_service import domain_service

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/domains", tags=["domains"])

class DomainStatusRequest(BaseModel):
    """域名状态请求模型"""
    domain: str
    is_processed: bool
    domain_type: str  # 'main_domain' 或 'subdomain'
    parent_domain: Optional[str] = None

class BatchDomainStatusRequest(BaseModel):
    """批量域名状态请求模型"""
    domain_statuses: Dict[str, Dict]  # {domain: {"is_processed": bool, "domain_type": str, "parent_domain": str}}

@router.post("/status")
async def save_domain_status(request: DomainStatusRequest):
    """
    保存单个域名的处理状态
    """
    try:
        result = await domain_service.save_domain_status(
            domain=request.domain,
            is_processed=request.is_processed,
            domain_type=request.domain_type,
            parent_domain=request.parent_domain
        )
        
        return {
            "success": result,
            "message": f"Domain status saved for {request.domain}"
        }
        
    except Exception as e:
        logger.error(f"Error saving domain status: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to save domain status: {str(e)}")

@router.post("/status/batch")
async def save_batch_domain_status(request: BatchDomainStatusRequest):
    """
    批量保存域名处理状态
    """
    try:
        result = await domain_service.batch_save_domain_statuses(request.domain_statuses)
        
        return {
            "success": result,
            "message": f"Batch saved {len(request.domain_statuses)} domain statuses"
        }
        
    except Exception as e:
        logger.error(f"Error batch saving domain statuses: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to batch save domain statuses: {str(e)}")

@router.get("/status/{domain}")
async def get_domain_status(domain: str):
    """
    获取单个域名的处理状态
    """
    try:
        status = await domain_service.get_domain_status(domain)
        
        return {
            "domain": domain,
            "is_processed": status
        }
        
    except Exception as e:
        logger.error(f"Error getting domain status: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get domain status: {str(e)}")

@router.get("/status")
async def get_all_domain_statuses():
    """
    获取所有域名的处理状态
    """
    try:
        statuses = await domain_service.get_all_domain_statuses()
        
        return {
            "domain_statuses": statuses,
            "total_count": len(statuses)
        }
        
    except Exception as e:
        logger.error(f"Error getting all domain statuses: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get domain statuses: {str(e)}")

@router.get("/processed")
async def get_processed_domains(domain_type: Optional[str] = None):
    """
    获取已处理的域名列表
    
    - **domain_type**: 可选，过滤域名类型 ('main_domain' 或 'subdomain')
    """
    try:
        domains = await domain_service.get_processed_domains(domain_type)
        
        return {
            "processed_domains": domains,
            "count": len(domains)
        }
        
    except Exception as e:
        logger.error(f"Error getting processed domains: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get processed domains: {str(e)}")

@router.get("/unprocessed")
async def get_unprocessed_domains(domain_type: Optional[str] = None):
    """
    获取未处理的域名列表
    
    - **domain_type**: 可选，过滤域名类型 ('main_domain' 或 'subdomain')
    """
    try:
        domains = await domain_service.get_unprocessed_domains(domain_type)
        
        return {
            "unprocessed_domains": domains,
            "count": len(domains)
        }
        
    except Exception as e:
        logger.error(f"Error getting unprocessed domains: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get unprocessed domains: {str(e)}")

@router.delete("/status/{domain}")
async def delete_domain_status(domain: str):
    """
    删除域名处理状态
    """
    try:
        result = await domain_service.delete_domain_status(domain)
        
        if result:
            return {
                "success": True,
                "message": f"Domain status deleted for {domain}"
            }
        else:
            return {
                "success": False,
                "message": f"Domain status not found for {domain}"
            }
        
    except Exception as e:
        logger.error(f"Error deleting domain status: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to delete domain status: {str(e)}")
