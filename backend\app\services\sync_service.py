"""
同步服务 - 定时从AWVS获取数据并存储到本地数据库
"""
import asyncio
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
import logging

from app.services.awvs_service import awvs_service
from app.services.database_service import database_service
from app.models.vulnerability import Vulnerability

logger = logging.getLogger(__name__)

class SyncService:
    """同步服务类"""
    
    def __init__(self):
        self.is_syncing = False
        self.last_sync_time = None
    
    async def full_sync(self) -> Dict[str, Any]:
        """
        完全同步 - 获取所有严重漏洞并存储到数据库
        """
        if self.is_syncing:
            logger.warning("Sync already in progress, skipping...")
            return {"status": "skipped", "message": "Sync already in progress"}
        
        self.is_syncing = True
        start_time = datetime.utcnow()
        sync_id = None
        
        try:
            logger.info("Starting full sync from AWVS...")
            
            # 记录同步开始
            sync_id = await database_service.save_sync_history(
                sync_type="full",
                start_time=start_time,
                status="running"
            )
            
            # 从AWVS获取所有严重漏洞
            logger.info("Fetching all critical vulnerabilities from AWVS...")
            vulnerabilities = await awvs_service.get_filtered_vulnerabilities(["critical"])
            
            if not vulnerabilities:
                logger.warning("No vulnerabilities received from AWVS")
                await database_service.update_sync_history(
                    sync_id=sync_id,
                    end_time=datetime.utcnow(),
                    status="success",
                    total_vulnerabilities=0,
                    new_vulnerabilities=0,
                    updated_vulnerabilities=0
                )
                return {
                    "status": "success",
                    "message": "No vulnerabilities found",
                    "total_vulnerabilities": 0,
                    "new_vulnerabilities": 0,
                    "updated_vulnerabilities": 0
                }
            
            logger.info(f"Received {len(vulnerabilities)} vulnerabilities from AWVS")
            
            # 保存到数据库
            save_result = await database_service.save_vulnerabilities(vulnerabilities)
            
            # 获取最新漏洞的发现时间
            latest_vuln_time = max(vuln.discovered_at for vuln in vulnerabilities) if vulnerabilities else None
            
            # 更新同步历史
            end_time = datetime.utcnow()
            await database_service.update_sync_history(
                sync_id=sync_id,
                end_time=end_time,
                status="success",
                total_vulnerabilities=len(vulnerabilities),
                new_vulnerabilities=save_result["new_count"],
                updated_vulnerabilities=save_result["updated_count"],
                last_vulnerability_time=latest_vuln_time
            )
            
            self.last_sync_time = end_time
            
            logger.info(f"Full sync completed successfully: {save_result}")
            
            return {
                "status": "success",
                "message": "Full sync completed successfully",
                "total_vulnerabilities": len(vulnerabilities),
                "new_vulnerabilities": save_result["new_count"],
                "updated_vulnerabilities": save_result["updated_count"],
                "sync_duration": (end_time - start_time).total_seconds()
            }
            
        except Exception as e:
            error_msg = f"Full sync failed: {str(e)}"
            logger.error(error_msg)
            
            # 更新同步历史为失败状态
            if sync_id:
                await database_service.update_sync_history(
                    sync_id=sync_id,
                    end_time=datetime.utcnow(),
                    status="failed",
                    error_message=error_msg
                )
            
            return {
                "status": "failed",
                "message": error_msg,
                "error": str(e)
            }
            
        finally:
            self.is_syncing = False
    
    async def incremental_sync(self) -> Dict[str, Any]:
        """
        增量同步 - 只获取自上次同步以来的新漏洞
        """
        if self.is_syncing:
            logger.warning("Sync already in progress, skipping...")
            return {"status": "skipped", "message": "Sync already in progress"}
        
        self.is_syncing = True
        start_time = datetime.utcnow()
        sync_id = None
        
        try:
            logger.info("Starting incremental sync from AWVS...")
            
            # 获取上次同步时间
            last_sync_time = await database_service.get_last_sync_time()
            
            if not last_sync_time:
                logger.info("No previous sync found, performing full sync instead")
                return await self.full_sync()
            
            logger.info(f"Last sync time: {last_sync_time}")
            
            # 记录同步开始
            sync_id = await database_service.save_sync_history(
                sync_type="incremental",
                start_time=start_time,
                status="running"
            )
            
            # 从AWVS获取新的严重漏洞
            # 注意：AWVS API可能不支持基于时间的过滤，所以我们获取所有数据然后在本地过滤
            logger.info("Fetching vulnerabilities from AWVS...")
            all_vulnerabilities = await awvs_service.get_filtered_vulnerabilities(["critical"])
            
            if not all_vulnerabilities:
                logger.warning("No vulnerabilities received from AWVS")
                await database_service.update_sync_history(
                    sync_id=sync_id,
                    end_time=datetime.utcnow(),
                    status="success",
                    total_vulnerabilities=0,
                    new_vulnerabilities=0,
                    updated_vulnerabilities=0
                )
                return {
                    "status": "success",
                    "message": "No new vulnerabilities found",
                    "total_vulnerabilities": 0,
                    "new_vulnerabilities": 0,
                    "updated_vulnerabilities": 0
                }
            
            # 过滤出新的漏洞（发现时间晚于上次同步时间）
            # 处理时区问题：确保两个时间都是同一类型
            if last_sync_time.tzinfo is None:
                # 如果last_sync_time是naive，将其转换为UTC
                from datetime import timezone
                last_sync_time = last_sync_time.replace(tzinfo=timezone.utc)

            new_vulnerabilities = []
            for vuln in all_vulnerabilities:
                vuln_time = vuln.discovered_at
                # 确保漏洞时间也有时区信息
                if vuln_time.tzinfo is None:
                    vuln_time = vuln_time.replace(tzinfo=timezone.utc)

                if vuln_time > last_sync_time:
                    new_vulnerabilities.append(vuln)
            
            logger.info(f"Found {len(new_vulnerabilities)} new vulnerabilities since last sync")
            
            if new_vulnerabilities:
                # 保存新漏洞到数据库
                save_result = await database_service.save_vulnerabilities(new_vulnerabilities)
                
                # 获取最新漏洞的发现时间
                latest_vuln_time = max(vuln.discovered_at for vuln in new_vulnerabilities)
            else:
                save_result = {"new_count": 0, "updated_count": 0}
                latest_vuln_time = last_sync_time
            
            # 更新同步历史
            end_time = datetime.utcnow()
            await database_service.update_sync_history(
                sync_id=sync_id,
                end_time=end_time,
                status="success",
                total_vulnerabilities=len(all_vulnerabilities),
                new_vulnerabilities=save_result["new_count"],
                updated_vulnerabilities=save_result["updated_count"],
                last_vulnerability_time=latest_vuln_time
            )
            
            self.last_sync_time = end_time
            
            logger.info(f"Incremental sync completed successfully: {save_result}")
            
            return {
                "status": "success",
                "message": "Incremental sync completed successfully",
                "total_vulnerabilities": len(all_vulnerabilities),
                "new_vulnerabilities": save_result["new_count"],
                "updated_vulnerabilities": save_result["updated_count"],
                "sync_duration": (end_time - start_time).total_seconds()
            }
            
        except Exception as e:
            error_msg = f"Incremental sync failed: {str(e)}"
            logger.error(error_msg)
            
            # 更新同步历史为失败状态
            if sync_id:
                await database_service.update_sync_history(
                    sync_id=sync_id,
                    end_time=datetime.utcnow(),
                    status="failed",
                    error_message=error_msg
                )
            
            return {
                "status": "failed",
                "message": error_msg,
                "error": str(e)
            }
            
        finally:
            self.is_syncing = False
    
    async def get_sync_status(self) -> Dict[str, Any]:
        """获取同步状态"""
        return {
            "is_syncing": self.is_syncing,
            "last_sync_time": self.last_sync_time.isoformat() if self.last_sync_time else None
        }

# 创建全局实例
sync_service = SyncService()
