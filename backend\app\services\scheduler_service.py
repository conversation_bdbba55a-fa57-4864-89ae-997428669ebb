"""
定时任务调度器 - 管理定时同步任务
"""
import asyncio
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.interval import IntervalTrigger
from datetime import datetime
import logging

from app.services.sync_service import sync_service

logger = logging.getLogger(__name__)

class SchedulerService:
    """定时任务调度器服务"""
    
    def __init__(self):
        self.scheduler = AsyncIOScheduler()
        self.is_running = False
    
    async def start(self):
        """启动调度器"""
        if self.is_running:
            logger.warning("Scheduler is already running")
            return
        
        try:
            # 添加定时任务：每30分钟执行一次增量同步
            self.scheduler.add_job(
                func=self._scheduled_sync,
                trigger=IntervalTrigger(minutes=30),
                id='incremental_sync',
                name='Incremental Vulnerability Sync',
                replace_existing=True,
                max_instances=1  # 确保同时只有一个同步任务运行
            )
            
            # 启动调度器
            self.scheduler.start()
            self.is_running = True
            
            logger.info("Scheduler started successfully")
            logger.info("Incremental sync scheduled to run every 30 minutes")
            
            # 启动时执行一次完整同步（如果数据库为空）
            asyncio.create_task(self._initial_sync())
            
        except Exception as e:
            logger.error(f"Failed to start scheduler: {str(e)}")
            raise
    
    async def stop(self):
        """停止调度器"""
        if not self.is_running:
            logger.warning("Scheduler is not running")
            return
        
        try:
            self.scheduler.shutdown(wait=True)
            self.is_running = False
            logger.info("Scheduler stopped successfully")
            
        except Exception as e:
            logger.error(f"Failed to stop scheduler: {str(e)}")
            raise
    
    async def _scheduled_sync(self):
        """定时执行的同步任务"""
        try:
            logger.info("Starting scheduled incremental sync...")
            result = await sync_service.incremental_sync()
            
            if result["status"] == "success":
                logger.info(f"Scheduled sync completed: {result['new_vulnerabilities']} new, {result['updated_vulnerabilities']} updated")
            else:
                logger.error(f"Scheduled sync failed: {result.get('message', 'Unknown error')}")
                
        except Exception as e:
            logger.error(f"Error in scheduled sync: {str(e)}")
    
    async def _initial_sync(self):
        """启动时的初始同步"""
        try:
            # 等待一小段时间确保应用完全启动
            await asyncio.sleep(5)
            
            logger.info("Performing initial sync check...")
            
            # 检查数据库中是否有数据
            from app.services.database_service import database_service
            stats = await database_service.get_vulnerability_stats()
            
            if stats["total_count"] == 0:
                logger.info("Database is empty, performing initial full sync...")
                result = await sync_service.full_sync()
                
                if result["status"] == "success":
                    logger.info(f"Initial sync completed: {result['total_vulnerabilities']} vulnerabilities loaded")
                else:
                    logger.error(f"Initial sync failed: {result.get('message', 'Unknown error')}")
            else:
                logger.info(f"Database already contains {stats['total_count']} vulnerabilities, skipping initial sync")
                
        except Exception as e:
            logger.error(f"Error in initial sync: {str(e)}")
    
    async def trigger_manual_sync(self, sync_type: str = "incremental") -> dict:
        """手动触发同步"""
        try:
            if sync_type == "full":
                logger.info("Triggering manual full sync...")
                result = await sync_service.full_sync()
            else:
                logger.info("Triggering manual incremental sync...")
                result = await sync_service.incremental_sync()
            
            return result
            
        except Exception as e:
            error_msg = f"Manual sync failed: {str(e)}"
            logger.error(error_msg)
            return {
                "status": "failed",
                "message": error_msg,
                "error": str(e)
            }
    
    def get_job_status(self) -> dict:
        """获取任务状态"""
        if not self.is_running:
            return {
                "scheduler_running": False,
                "jobs": []
            }
        
        jobs = []
        for job in self.scheduler.get_jobs():
            jobs.append({
                "id": job.id,
                "name": job.name,
                "next_run_time": job.next_run_time.isoformat() if job.next_run_time else None,
                "trigger": str(job.trigger)
            })
        
        return {
            "scheduler_running": self.is_running,
            "jobs": jobs
        }
    
    async def reschedule_sync(self, interval_minutes: int = 30):
        """重新调度同步任务"""
        try:
            if self.is_running:
                # 移除现有任务
                self.scheduler.remove_job('incremental_sync')
                
                # 添加新的任务
                self.scheduler.add_job(
                    func=self._scheduled_sync,
                    trigger=IntervalTrigger(minutes=interval_minutes),
                    id='incremental_sync',
                    name='Incremental Vulnerability Sync',
                    replace_existing=True,
                    max_instances=1
                )
                
                logger.info(f"Sync rescheduled to run every {interval_minutes} minutes")
                return True
            else:
                logger.warning("Scheduler is not running, cannot reschedule")
                return False
                
        except Exception as e:
            logger.error(f"Failed to reschedule sync: {str(e)}")
            return False

# 创建全局实例
scheduler_service = SchedulerService()
