import { createRouter, createWebHistory } from 'vue-router'
import Dashboard from '@/views/Dashboard.vue'
import VulnerabilityList from '@/views/VulnerabilityList.vue'
import VulnerabilityDetail from '@/views/VulnerabilityDetail.vue'
import VulnerabilityByDomain from '@/views/VulnerabilityByDomain.vue'

const routes = [
  {
    path: '/',
    name: 'Dashboard',
    component: Dashboard,
    meta: {
      title: '仪表盘'
    }
  },
  {
    path: '/vulnerabilities',
    name: 'VulnerabilityList',
    component: VulnerabilityList,
    meta: {
      title: '漏洞列表'
    }
  },
  {
    path: '/vulnerabilities/:id',
    name: 'VulnerabilityDetail',
    component: VulnerabilityDetail,
    meta: {
      title: '漏洞详情'
    }
  },
  {
    path: '/vulnerabilities-by-domain',
    name: 'VulnerabilityByDomain',
    component: VulnerabilityByDomain,
    meta: {
      title: '按域名分类'
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫 - 设置页面标题
router.beforeEach((to, from, next) => {
  document.title = to.meta.title ? `${to.meta.title} - AWVS 漏洞管理系统` : 'AWVS 漏洞管理系统'
  next()
})

export default router
