<template>
  <div class="vulnerability-by-domain">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">按域名分类的漏洞</h1>
        <p class="page-description">按主域名和子域名层级组织显示漏洞</p>
      </div>
      <button @click="refreshAllCriticalVulnerabilities" class="btn btn-primary" :disabled="isLoading">
        <RefreshCw :size="16" :class="{ 'animate-spin': isLoading }" />
        刷新
      </button>
    </div>

    <!-- 统计信息 -->
    <div v-if="stats.total_count > 0" class="stats-grid">
      <div class="stat-card">
        <div class="stat-label">总域名数</div>
        <div class="stat-value">{{ Object.keys(domainGroups).length }}</div>
      </div>
      <div class="stat-card">
        <div class="stat-label">总漏洞数</div>
        <div class="stat-value">{{ stats.total_count }}</div>
      </div>
      <div class="stat-card">
        <div class="stat-label">严重漏洞</div>
        <div class="stat-value critical">{{ stats.critical_count }}</div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="isLoading" class="loading-state">
      <div class="loading-spinner"></div>
      <p>正在加载漏洞数据...</p>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="hasError" class="error-state">
      <AlertCircle :size="48" />
      <h3>加载失败</h3>
      <p>{{ error }}</p>
      <button @click="refreshAllCriticalVulnerabilities" class="btn btn-primary">
        <RefreshCw :size="16" />
        重试
      </button>
    </div>

    <!-- 域名分组列表 -->
    <div v-else-if="Object.keys(domainGroups).length > 0" class="domain-groups">
      <div
        v-for="(group, domain) in domainGroups"
        :key="domain"
        :class="['domain-group', { 'processed': processedDomains.has(domain) }]"
      >
        <!-- 域名标题 -->
        <div class="domain-header">
          <div class="domain-info" @click="toggleDomain(domain)">
            <Globe :size="20" />
            <div class="domain-details">
              <h2 class="domain-name">{{ domain }}</h2>
              <span class="domain-stats">
                {{ group.totalVulnerabilities }} 个漏洞
                <span v-if="group.subdomains.length > 0">
                  · {{ group.subdomains.length }} 个子域名
                </span>
              </span>
            </div>
          </div>
          <div class="domain-actions">
            <!-- 处理状态按钮 -->
            <button
              @click.stop="toggleDomainProcessed(domain)"
              :class="['status-btn', { 'processed': processedDomains.has(domain) }]"
              :title="processedDomains.has(domain) ? '标记为未处理' : '标记为已处理'"
            >
              <CheckCircle v-if="processedDomains.has(domain)" :size="16" />
              <Circle v-else :size="16" />
              <span>{{ processedDomains.has(domain) ? '已处理' : '未处理' }}</span>
            </button>

            <div class="severity-summary">
              <span v-if="group.criticalCount > 0" class="severity-count critical">
                {{ group.criticalCount }} 严重
              </span>
              <span v-if="group.highCount > 0" class="severity-count high">
                {{ group.highCount }} 高危
              </span>
            </div>
            <ChevronDown
              :size="20"
              :class="['expand-icon', { 'expanded': expandedDomains.has(domain) }]"
              @click="toggleDomain(domain)"
            />
          </div>
        </div>

        <!-- 展开的域名内容 -->
        <div v-if="expandedDomains.has(domain)" class="domain-content">
          <!-- 子域名及其漏洞 -->
          <div v-if="group.subdomains.length > 0" class="subdomains">
            <div
              v-for="subdomain in group.subdomains"
              :key="subdomain.name"
              :class="['subdomain-section', { 'processed': processedSubdomains.has(subdomain.name) }]"
            >
              <div class="subdomain-header">
                <div class="subdomain-info" @click="toggleSubdomain(domain, subdomain.name)">
                  <div class="subdomain-icon">
                    <div class="subdomain-dot"></div>
                  </div>
                  <div class="subdomain-details">
                    <h4 class="subdomain-name">{{ subdomain.name }}</h4>
                    <span class="subdomain-stats">{{ subdomain.vulnerabilities.length }} 个漏洞</span>
                  </div>
                </div>
                <div class="subdomain-actions">
                  <!-- 处理状态按钮 -->
                  <button
                    @click.stop="toggleSubdomainProcessed(subdomain.name)"
                    :class="['status-btn', 'subdomain-status', { 'processed': processedSubdomains.has(subdomain.name) }]"
                    :title="processedSubdomains.has(subdomain.name) ? '标记为未处理' : '标记为已处理'"
                  >
                    <CheckCircle v-if="processedSubdomains.has(subdomain.name)" :size="14" />
                    <Circle v-else :size="14" />
                    <span>{{ processedSubdomains.has(subdomain.name) ? '已处理' : '未处理' }}</span>
                  </button>

                  <div class="severity-summary">
                    <span v-if="subdomain.criticalCount > 0" class="severity-count critical">
                      {{ subdomain.criticalCount }} 严重
                    </span>
                    <span v-if="subdomain.highCount > 0" class="severity-count high">
                      {{ subdomain.highCount }} 高危
                    </span>
                  </div>
                  <ChevronDown
                    :size="16"
                    :class="['expand-icon', { 'expanded': expandedSubdomains.has(`${domain}:${subdomain.name}`) }]"
                    @click="toggleSubdomain(domain, subdomain.name)"
                  />
                </div>
              </div>

              <!-- 子域名漏洞列表 -->
              <div v-if="expandedSubdomains.has(`${domain}:${subdomain.name}`)" class="subdomain-vulnerabilities">
                <div class="vulnerability-grid">
                  <div 
                    v-for="vulnerability in subdomain.vulnerabilities" 
                    :key="vulnerability.id"
                    class="vulnerability-card"
                    @click="goToDetail(vulnerability.id)"
                  >
                    <div class="vulnerability-header">
                      <h4 class="vulnerability-name">{{ vulnerability.name }}</h4>
                      <div :class="['severity-badge', getSeverityClass(vulnerability.severity)]">
                        {{ getSeverityLabel(vulnerability.severity) }}
                      </div>
                    </div>
                    <div class="vulnerability-meta">
                      <div class="meta-item">
                        <Globe :size="14" />
                        <span>{{ vulnerability.target_url }}</span>
                      </div>
                      <div class="meta-item">
                        <Calendar :size="14" />
                        <span>{{ formatDate(vulnerability.discovered_at) }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else class="empty-state">
      <Shield :size="64" />
      <h3>暂无漏洞数据</h3>
      <p>当前没有发现任何漏洞，系统安全状态良好</p>
      <button @click="refreshAllCriticalVulnerabilities" class="btn btn-primary">
        <RefreshCw :size="16" />
        同步数据
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import {
  RefreshCw,
  Globe,
  Calendar,
  AlertCircle,
  Shield,
  ChevronDown,
  CheckCircle,
  Circle,
  Code,
  Copy,
  Check
} from 'lucide-vue-next'
import { useVulnerabilityStore } from '@/stores/vulnerability'
import { storeToRefs } from 'pinia'
import { getAllDomainStatuses, saveDomainStatus } from '../services/domainService'

const router = useRouter()
const vulnerabilityStore = useVulnerabilityStore()

const {
  vulnerabilities,
  stats,
  isLoading,
  hasError,
  error
} = storeToRefs(vulnerabilityStore)

const {
  fetchVulnerabilities,
  getSeverityLabel,
  getSeverityClass,
  formatDate
} = vulnerabilityStore

// 刷新函数，只重新获取当前页面数据
async function refreshAllCriticalVulnerabilities() {
  // 重新获取最新数据
  await fetchVulnerabilities({
    severity: ['critical'],
    status: ['open'],
    limit: 5000  // 设置足够大的数字获取所有严重漏洞
  })
}

// 本地状态
const expandedDomains = ref(new Set())
const expandedSubdomains = ref(new Set())

// 域名处理状态管理
const processedDomains = ref(new Set())
const processedSubdomains = ref(new Set())

// 从数据库加载处理状态
async function loadProcessedStatus() {
  try {
    const response = await getAllDomainStatuses()
    const domainStatuses = response.domain_statuses || {}

    // 清空现有状态
    processedDomains.value.clear()
    processedSubdomains.value.clear()

    // 根据数据库状态更新本地状态
    for (const [domain, isProcessed] of Object.entries(domainStatuses)) {
      if (isProcessed) {
        // 判断是主域名还是子域名
        if (isMainDomain(domain)) {
          processedDomains.value.add(domain)
        } else {
          processedSubdomains.value.add(domain)
        }
      }
    }
  } catch (error) {
    console.warn('Failed to load processed status from database:', error)
  }
}

// 保存处理状态到数据库
async function saveProcessedStatus(domain, isProcessed, domainType, parentDomain = null) {
  try {
    await saveDomainStatus(domain, isProcessed, domainType, parentDomain)
  } catch (error) {
    console.warn('Failed to save processed status to database:', error)
  }
}

// 切换主域名处理状态
async function toggleDomainProcessed(domain) {
  const isCurrentlyProcessed = processedDomains.value.has(domain)
  const newStatus = !isCurrentlyProcessed

  if (newStatus) {
    processedDomains.value.add(domain)
  } else {
    processedDomains.value.delete(domain)
  }

  // 保存到数据库
  await saveProcessedStatus(domain, newStatus, 'main_domain')
}

// 切换子域名处理状态
async function toggleSubdomainProcessed(subdomain) {
  const isCurrentlyProcessed = processedSubdomains.value.has(subdomain)
  const newStatus = !isCurrentlyProcessed

  if (newStatus) {
    processedSubdomains.value.add(subdomain)
  } else {
    processedSubdomains.value.delete(subdomain)
  }

  // 获取父域名
  const parentDomain = getMainDomain(subdomain)

  // 保存到数据库
  await saveProcessedStatus(subdomain, newStatus, 'subdomain', parentDomain)
}

// 提取域名的函数
function extractDomain(url) {
  if (!url) return 'unknown'
  
  try {
    const urlObj = new URL(url.startsWith('http') ? url : `http://${url}`)
    return urlObj.hostname.toLowerCase()
  } catch {
    // 如果URL解析失败，尝试简单的字符串处理
    const match = url.match(/(?:https?:\/\/)?([^\/\s]+)/i)
    return match ? match[1].toLowerCase() : 'unknown'
  }
}

// 获取主域名（例如：从 www.example.com 获取 example.com）
function getMainDomain(hostname) {
  if (!hostname || hostname === 'unknown') return hostname

  const parts = hostname.split('.')
  if (parts.length <= 2) return hostname

  // 泰国域名的特殊处理
  if (parts[parts.length - 1] === 'th') {
    // .ac.th, .co.th, .go.th, .or.th 等都是泰国的二级域名
    const secondLevelDomains = ['ac', 'co', 'go', 'or', 'net', 'in']
    if (parts.length >= 3 && secondLevelDomains.includes(parts[parts.length - 2])) {
      // 对于 xxx.go.th 这样的域名，主域名应该是 xxx.go.th
      return parts.slice(-3).join('.')
    }
  }

  // 对于其他情况，取最后两部分作为主域名
  return parts.slice(-2).join('.')
}

// 检查是否为子域名
function isSubdomain(hostname, mainDomain) {
  if (!hostname || !mainDomain || hostname === mainDomain) return false
  return hostname.endsWith('.' + mainDomain)
}

// 检查是否为主域名
function isMainDomain(hostname) {
  if (!hostname) return false

  // 获取该hostname的主域名
  const mainDomain = getMainDomain(hostname)

  // 如果hostname等于其主域名，则它是主域名
  return hostname === mainDomain
}

// 按域名分组的计算属性
const domainGroups = computed(() => {
  const groups = {}

  vulnerabilities.value.forEach(vuln => {
    const hostname = extractDomain(vuln.target_url)
    const mainDomain = getMainDomain(hostname)

    // 初始化主域名组
    if (!groups[mainDomain]) {
      groups[mainDomain] = {
        subdomains: [],
        totalVulnerabilities: 0,
        criticalCount: 0,
        highCount: 0
      }
    }

    // 统计主域名的总漏洞数（包括所有子域名）
    groups[mainDomain].totalVulnerabilities++
    if (vuln.severity === 'critical') {
      groups[mainDomain].criticalCount++
    } else if (vuln.severity === 'high') {
      groups[mainDomain].highCount++
    }

    // 所有漏洞都归类到子域名（包括主域名本身）
    let subdomain = groups[mainDomain].subdomains.find(sub => sub.name === hostname)
    if (!subdomain) {
      subdomain = {
        name: hostname,
        vulnerabilities: [],
        criticalCount: 0,
        highCount: 0
      }
      groups[mainDomain].subdomains.push(subdomain)
    }

    subdomain.vulnerabilities.push(vuln)
    if (vuln.severity === 'critical') {
      subdomain.criticalCount++
    } else if (vuln.severity === 'high') {
      subdomain.highCount++
    }
  })

  // 对子域名进行排序，主域名本身排在最前面
  Object.values(groups).forEach(group => {
    group.subdomains.sort((a, b) => {
      // 主域名本身排在最前面
      const aIsMain = Object.keys(groups).includes(a.name)
      const bIsMain = Object.keys(groups).includes(b.name)

      if (aIsMain && !bIsMain) return -1
      if (!aIsMain && bIsMain) return 1

      return a.name.localeCompare(b.name)
    })
  })

  return groups
})

// 方法
function toggleDomain(domain) {
  if (expandedDomains.value.has(domain)) {
    expandedDomains.value.delete(domain)
  } else {
    expandedDomains.value.add(domain)
  }
}

function toggleSubdomain(domain, subdomain) {
  const key = `${domain}:${subdomain}`
  if (expandedSubdomains.value.has(key)) {
    expandedSubdomains.value.delete(key)
  } else {
    expandedSubdomains.value.add(key)
  }
}

function goToDetail(id) {
  // 在新标签页中打开漏洞详情
  const url = router.resolve(`/vulnerabilities/${id}`).href
  window.open(url, '_blank')
}

onMounted(async () => {
  // 加载处理状态
  await loadProcessedStatus()

  // 获取所有严重漏洞用于域名分类
  await fetchVulnerabilities({
    severity: ['critical'],
    status: ['open'],
    limit: 5000  // 设置足够大的数字获取所有严重漏洞
  })
})
</script>

<style scoped>
.vulnerability-by-domain {
  max-width: 1200px;
  margin: 0 auto;
}

/* 页面标题 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-xl);
  gap: var(--spacing-lg);
}

.header-content h1 {
  margin: 0 0 var(--spacing-xs);
  color: var(--text-primary);
  font-size: var(--font-size-2xl);
  font-weight: 700;
}

.header-content p {
  margin: 0;
  color: var(--text-secondary);
  font-size: var(--font-size-base);
}

/* 统计卡片 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

.stat-card {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: var(--spacing-lg);
  text-align: center;
}

.stat-label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
}

.stat-value {
  font-size: var(--font-size-2xl);
  font-weight: 700;
  color: var(--text-primary);
}

.stat-value.critical {
  color: var(--critical-color);
}

/* 加载和错误状态 */
.loading-state,
.error-state,
.empty-state {
  text-align: center;
  padding: var(--spacing-2xl);
  color: var(--text-secondary);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--border-color);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto var(--spacing-lg);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 域名分组 */
.domain-groups {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.domain-group {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  overflow: hidden;
  transition: all 0.3s ease;
}

/* 已处理的主域名样式 */
.domain-group.processed {
  border-color: #10b981;
  background: linear-gradient(135deg, #ecfdf5 0%, var(--bg-primary) 100%);
}

.domain-group.processed .domain-header {
  background: rgba(16, 185, 129, 0.05);
}

.domain-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg);
  cursor: pointer;
  transition: background-color 0.2s ease;
  border-bottom: 1px solid var(--border-color);
}

.domain-header:hover {
  background: var(--bg-secondary);
}

.domain-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.domain-details h2 {
  margin: 0 0 var(--spacing-xs);
  color: var(--text-primary);
  font-size: var(--font-size-lg);
  font-weight: 600;
}

.domain-stats {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.domain-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

/* 处理状态按钮样式 */
.status-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-xs) var(--spacing-sm);
  border: 1px solid #d1d5db;
  border-radius: var(--border-radius);
  background: white;
  color: #6b7280;
  font-size: var(--font-size-xs);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.status-btn:hover {
  border-color: #9ca3af;
  background: #f9fafb;
}

.status-btn.processed {
  border-color: #10b981;
  background: #ecfdf5;
  color: #065f46;
}

.status-btn.processed:hover {
  border-color: #059669;
  background: #d1fae5;
}

/* 子域名状态按钮稍小一些 */
.status-btn.subdomain-status {
  padding: 4px 8px;
  font-size: 11px;
}

.severity-summary {
  display: flex;
  gap: var(--spacing-sm);
}

.severity-count {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius);
  font-size: var(--font-size-xs);
  font-weight: 600;
}

.severity-count.critical {
  background: var(--critical-color);
  color: white;
}

.severity-count.high {
  background: var(--high-color);
  color: white;
}

.expand-icon {
  transition: transform 0.2s ease;
}

.expand-icon.expanded {
  transform: rotate(180deg);
}

/* 域名内容 */
.domain-content {
  padding: var(--spacing-lg);
  background: var(--bg-secondary);
}

.section-title {
  margin: 0 0 var(--spacing-lg);
  color: var(--text-primary);
  font-size: var(--font-size-base);
  font-weight: 600;
}

.direct-vulnerabilities {
  margin-bottom: var(--spacing-xl);
}

/* 子域名部分 */
.subdomains {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.subdomain-section {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  overflow: hidden;
  transition: all 0.3s ease;
}

/* 已处理的子域名样式 */
.subdomain-section.processed {
  border-color: #10b981;
  background: linear-gradient(135deg, #f0fdf4 0%, var(--bg-primary) 100%);
}

.subdomain-section.processed .subdomain-header {
  background: rgba(16, 185, 129, 0.03);
}

.subdomain-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md);
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.subdomain-header:hover {
  background: var(--bg-secondary);
}

.subdomain-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.subdomain-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
}

.subdomain-dot {
  width: 8px;
  height: 8px;
  background: var(--primary-color);
  border-radius: 50%;
}

.subdomain-details h4 {
  margin: 0 0 var(--spacing-xs);
  color: var(--text-primary);
  font-size: var(--font-size-base);
  font-weight: 500;
}

.subdomain-stats {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.subdomain-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.subdomain-vulnerabilities {
  padding: var(--spacing-md);
  background: var(--bg-secondary);
}

/* 漏洞网格 */
.vulnerability-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: var(--spacing-md);
}

.vulnerability-card {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: var(--spacing-md);
  cursor: pointer;
  transition: all 0.2s ease;
}

.vulnerability-card:hover {
  border-color: var(--primary-color);
  box-shadow: var(--shadow-md);
}

.vulnerability-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-sm);
  gap: var(--spacing-sm);
}

.vulnerability-name {
  margin: 0;
  color: var(--text-primary);
  font-size: var(--font-size-sm);
  font-weight: 600;
  line-height: 1.4;
  flex: 1;
}

.vulnerability-meta {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.meta-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
}

.meta-item span {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 严重程度徽章 */
.severity-badge {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius);
  font-size: var(--font-size-xs);
  font-weight: 600;
  white-space: nowrap;
}

.severity-badge.critical {
  background: var(--critical-color);
  color: white;
}

.severity-badge.high {
  background: var(--high-color);
  color: white;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: stretch;
  }

  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }

  .domain-header,
  .subdomain-header {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-sm);
  }

  .domain-actions,
  .subdomain-actions {
    justify-content: space-between;
  }

  .vulnerability-grid {
    grid-template-columns: 1fr;
  }
}
</style>
