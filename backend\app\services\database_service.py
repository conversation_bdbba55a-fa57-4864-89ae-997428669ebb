"""
数据库服务 - 处理漏洞数据的CRUD操作
"""
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func, desc
from sqlalchemy.dialects.sqlite import insert
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
import logging

from app.models.db_models import VulnerabilityDB, DomainProcessingStatus, SyncHistory
from app.models.vulnerability import Vulnerability, SeverityLevel, VulnerabilityStatus
from app.database import AsyncSessionLocal

logger = logging.getLogger(__name__)

class DatabaseService:
    """数据库服务类"""
    
    async def get_session(self) -> AsyncSession:
        """获取数据库会话"""
        return AsyncSessionLocal()
    
    async def save_vulnerabilities(self, vulnerabilities: List[Vulnerability]) -> Dict[str, int]:
        """
        批量保存漏洞数据，使用UPSERT操作
        返回新增和更新的数量
        """
        async with await self.get_session() as session:
            try:
                new_count = 0
                updated_count = 0
                
                for vuln in vulnerabilities:
                    # 检查漏洞是否已存在
                    existing = await session.get(VulnerabilityDB, vuln.id)
                    
                    if existing:
                        # 更新现有记录
                        existing.name = vuln.name
                        existing.description = vuln.description
                        existing.severity = vuln.severity.value
                        existing.status = vuln.status.value
                        existing.target_url = vuln.target_url
                        existing.target_name = vuln.target_name
                        existing.discovered_at = vuln.discovered_at
                        existing.last_seen = vuln.last_seen
                        existing.cvss_score = vuln.cvss_score
                        existing.cve_id = vuln.cve_id
                        existing.category = vuln.category
                        existing.recommendation = vuln.recommendation
                        existing.http_request = vuln.http_request
                        existing.updated_at = datetime.utcnow()
                        updated_count += 1
                    else:
                        # 创建新记录
                        db_vuln = VulnerabilityDB(
                            id=vuln.id,
                            name=vuln.name,
                            description=vuln.description,
                            severity=vuln.severity.value,
                            status=vuln.status.value,
                            target_url=vuln.target_url,
                            target_name=vuln.target_name,
                            discovered_at=vuln.discovered_at,
                            last_seen=vuln.last_seen,
                            cvss_score=vuln.cvss_score,
                            cve_id=vuln.cve_id,
                            category=vuln.category,
                            recommendation=vuln.recommendation,
                            http_request=vuln.http_request
                        )
                        session.add(db_vuln)
                        new_count += 1
                
                await session.commit()
                logger.info(f"Saved vulnerabilities: {new_count} new, {updated_count} updated")
                
                return {
                    "new_count": new_count,
                    "updated_count": updated_count,
                    "total_count": new_count + updated_count
                }
                
            except Exception as e:
                await session.rollback()
                logger.error(f"Error saving vulnerabilities: {str(e)}")
                raise
    
    async def get_vulnerabilities(
        self,
        severity_filter: Optional[List[str]] = None,
        status_filter: Optional[List[str]] = None,
        limit: int = 100,
        offset: int = 0,
        since_date: Optional[datetime] = None
    ) -> List[Vulnerability]:
        """
        从数据库获取漏洞列表
        """
        async with await self.get_session() as session:
            try:
                query = select(VulnerabilityDB)
                
                # 添加过滤条件
                conditions = []
                
                if severity_filter:
                    conditions.append(VulnerabilityDB.severity.in_(severity_filter))
                
                if status_filter:
                    conditions.append(VulnerabilityDB.status.in_(status_filter))
                
                if since_date:
                    conditions.append(VulnerabilityDB.discovered_at >= since_date)
                
                if conditions:
                    query = query.where(and_(*conditions))
                
                # 排序和分页
                query = query.order_by(desc(VulnerabilityDB.discovered_at))
                query = query.offset(offset).limit(limit)
                
                result = await session.execute(query)
                db_vulns = result.scalars().all()
                
                # 转换为Pydantic模型
                vulnerabilities = []
                for db_vuln in db_vulns:
                    vuln = Vulnerability(
                        id=db_vuln.id,
                        name=db_vuln.name,
                        description=db_vuln.description,
                        severity=SeverityLevel(db_vuln.severity),
                        status=VulnerabilityStatus(db_vuln.status),
                        target_url=db_vuln.target_url,
                        target_name=db_vuln.target_name,
                        discovered_at=db_vuln.discovered_at,
                        last_seen=db_vuln.last_seen,
                        cvss_score=db_vuln.cvss_score,
                        cve_id=db_vuln.cve_id,
                        category=db_vuln.category,
                        recommendation=db_vuln.recommendation,
                        http_request=db_vuln.http_request
                    )
                    vulnerabilities.append(vuln)
                
                return vulnerabilities
                
            except Exception as e:
                logger.error(f"Error getting vulnerabilities: {str(e)}")
                raise
    
    async def get_vulnerability_by_id(self, vulnerability_id: str) -> Optional[Vulnerability]:
        """根据ID获取单个漏洞"""
        async with await self.get_session() as session:
            try:
                db_vuln = await session.get(VulnerabilityDB, vulnerability_id)
                
                if not db_vuln:
                    return None
                
                return Vulnerability(
                    id=db_vuln.id,
                    name=db_vuln.name,
                    description=db_vuln.description,
                    severity=SeverityLevel(db_vuln.severity),
                    status=VulnerabilityStatus(db_vuln.status),
                    target_url=db_vuln.target_url,
                    target_name=db_vuln.target_name,
                    discovered_at=db_vuln.discovered_at,
                    last_seen=db_vuln.last_seen,
                    cvss_score=db_vuln.cvss_score,
                    cve_id=db_vuln.cve_id,
                    category=db_vuln.category,
                    recommendation=db_vuln.recommendation,
                    http_request=db_vuln.http_request
                )
                
            except Exception as e:
                logger.error(f"Error getting vulnerability {vulnerability_id}: {str(e)}")
                raise

    async def get_vulnerability_stats(self) -> Dict[str, int]:
        """获取漏洞统计信息"""
        async with await self.get_session() as session:
            try:
                # 总数统计
                total_query = select(func.count(VulnerabilityDB.id))
                total_result = await session.execute(total_query)
                total_count = total_result.scalar()

                # 按严重程度统计
                severity_query = select(
                    VulnerabilityDB.severity,
                    func.count(VulnerabilityDB.id)
                ).group_by(VulnerabilityDB.severity)
                severity_result = await session.execute(severity_query)
                severity_stats = dict(severity_result.all())

                # 按状态统计
                status_query = select(
                    VulnerabilityDB.status,
                    func.count(VulnerabilityDB.id)
                ).group_by(VulnerabilityDB.status)
                status_result = await session.execute(status_query)
                status_stats = dict(status_result.all())

                return {
                    "total_count": total_count or 0,
                    "critical_count": severity_stats.get("critical", 0),
                    "high_count": severity_stats.get("high", 0),
                    "medium_count": severity_stats.get("medium", 0),
                    "low_count": severity_stats.get("low", 0),
                    "info_count": severity_stats.get("info", 0),
                    "open_count": status_stats.get("open", 0),
                    "fixed_count": status_stats.get("fixed", 0),
                    "accepted_count": status_stats.get("accepted", 0),
                    "ignored_count": status_stats.get("ignored", 0)
                }

            except Exception as e:
                logger.error(f"Error getting vulnerability stats: {str(e)}")
                raise

    async def get_last_sync_time(self) -> Optional[datetime]:
        """获取最后一次成功同步的时间"""
        async with await self.get_session() as session:
            try:
                query = select(SyncHistory.last_vulnerability_time).where(
                    SyncHistory.status == "success"
                ).order_by(desc(SyncHistory.end_time)).limit(1)

                result = await session.execute(query)
                last_time = result.scalar()

                return last_time

            except Exception as e:
                logger.error(f"Error getting last sync time: {str(e)}")
                return None

    async def save_sync_history(
        self,
        sync_type: str,
        start_time: datetime,
        end_time: Optional[datetime] = None,
        status: str = "running",
        total_vulnerabilities: int = 0,
        new_vulnerabilities: int = 0,
        updated_vulnerabilities: int = 0,
        error_message: Optional[str] = None,
        last_vulnerability_time: Optional[datetime] = None
    ) -> int:
        """保存同步历史记录"""
        async with await self.get_session() as session:
            try:
                sync_record = SyncHistory(
                    sync_type=sync_type,
                    start_time=start_time,
                    end_time=end_time,
                    status=status,
                    total_vulnerabilities=total_vulnerabilities,
                    new_vulnerabilities=new_vulnerabilities,
                    updated_vulnerabilities=updated_vulnerabilities,
                    error_message=error_message,
                    last_vulnerability_time=last_vulnerability_time
                )

                session.add(sync_record)
                await session.commit()
                await session.refresh(sync_record)

                return sync_record.id

            except Exception as e:
                await session.rollback()
                logger.error(f"Error saving sync history: {str(e)}")
                raise

    async def update_sync_history(
        self,
        sync_id: int,
        end_time: Optional[datetime] = None,
        status: Optional[str] = None,
        total_vulnerabilities: Optional[int] = None,
        new_vulnerabilities: Optional[int] = None,
        updated_vulnerabilities: Optional[int] = None,
        error_message: Optional[str] = None,
        last_vulnerability_time: Optional[datetime] = None
    ):
        """更新同步历史记录"""
        async with await self.get_session() as session:
            try:
                sync_record = await session.get(SyncHistory, sync_id)
                if not sync_record:
                    raise ValueError(f"Sync record {sync_id} not found")

                if end_time is not None:
                    sync_record.end_time = end_time
                if status is not None:
                    sync_record.status = status
                if total_vulnerabilities is not None:
                    sync_record.total_vulnerabilities = total_vulnerabilities
                if new_vulnerabilities is not None:
                    sync_record.new_vulnerabilities = new_vulnerabilities
                if updated_vulnerabilities is not None:
                    sync_record.updated_vulnerabilities = updated_vulnerabilities
                if error_message is not None:
                    sync_record.error_message = error_message
                if last_vulnerability_time is not None:
                    sync_record.last_vulnerability_time = last_vulnerability_time

                await session.commit()

            except Exception as e:
                await session.rollback()
                logger.error(f"Error updating sync history: {str(e)}")
                raise

    async def update_vulnerability_http_request(self, vulnerability_id: str, http_request: str) -> bool:
        """更新漏洞的HTTP Request数据"""
        async with await self.get_session() as session:
            try:
                # 查找漏洞记录
                query = select(VulnerabilityDB).where(VulnerabilityDB.id == vulnerability_id)
                result = await session.execute(query)
                vulnerability = result.scalar_one_or_none()

                if vulnerability:
                    # 更新HTTP Request数据
                    vulnerability.http_request = http_request
                    await session.commit()
                    logger.info(f"Updated HTTP Request data for vulnerability {vulnerability_id}")
                    return True
                else:
                    logger.warning(f"Vulnerability {vulnerability_id} not found for HTTP Request update")
                    return False

            except Exception as e:
                await session.rollback()
                logger.error(f"Error updating HTTP Request for vulnerability {vulnerability_id}: {str(e)}")
                return False

# 创建全局实例
database_service = DatabaseService()
