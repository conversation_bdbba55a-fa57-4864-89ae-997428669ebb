/**
 * 域名处理状态服务
 */
import axios from 'axios'

const API_BASE_URL = 'http://localhost:8000/api/domains'

// 创建axios实例
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

/**
 * 保存单个域名的处理状态
 * @param {string} domain - 域名
 * @param {boolean} isProcessed - 是否已处理
 * @param {string} domainType - 域名类型 ('main_domain' 或 'subdomain')
 * @param {string|null} parentDomain - 父域名（仅对子域名有效）
 */
export async function saveDomainStatus(domain, isProcessed, domainType, parentDomain = null) {
  try {
    const response = await api.post('/status', {
      domain,
      is_processed: isProcessed,
      domain_type: domainType,
      parent_domain: parentDomain
    })
    return response.data
  } catch (error) {
    console.error('Error saving domain status:', error)
    throw error
  }
}

/**
 * 批量保存域名处理状态
 * @param {Object} domainStatuses - 域名状态对象 {domain: {is_processed, domain_type, parent_domain}}
 */
export async function batchSaveDomainStatus(domainStatuses) {
  try {
    const response = await api.post('/status/batch', {
      domain_statuses: domainStatuses
    })
    return response.data
  } catch (error) {
    console.error('Error batch saving domain statuses:', error)
    throw error
  }
}

/**
 * 获取单个域名的处理状态
 * @param {string} domain - 域名
 */
export async function getDomainStatus(domain) {
  try {
    const response = await api.get(`/status/${encodeURIComponent(domain)}`)
    return response.data
  } catch (error) {
    console.error('Error getting domain status:', error)
    throw error
  }
}

/**
 * 获取所有域名的处理状态
 */
export async function getAllDomainStatuses() {
  try {
    const response = await api.get('/status')
    return response.data
  } catch (error) {
    console.error('Error getting all domain statuses:', error)
    throw error
  }
}

/**
 * 获取已处理的域名列表
 * @param {string|null} domainType - 可选，过滤域名类型
 */
export async function getProcessedDomains(domainType = null) {
  try {
    const params = domainType ? { domain_type: domainType } : {}
    const response = await api.get('/processed', { params })
    return response.data
  } catch (error) {
    console.error('Error getting processed domains:', error)
    throw error
  }
}

/**
 * 获取未处理的域名列表
 * @param {string|null} domainType - 可选，过滤域名类型
 */
export async function getUnprocessedDomains(domainType = null) {
  try {
    const params = domainType ? { domain_type: domainType } : {}
    const response = await api.get('/unprocessed', { params })
    return response.data
  } catch (error) {
    console.error('Error getting unprocessed domains:', error)
    throw error
  }
}

/**
 * 删除域名处理状态
 * @param {string} domain - 域名
 */
export async function deleteDomainStatus(domain) {
  try {
    const response = await api.delete(`/status/${encodeURIComponent(domain)}`)
    return response.data
  } catch (error) {
    console.error('Error deleting domain status:', error)
    throw error
  }
}
