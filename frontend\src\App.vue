<template>
  <div id="app">
    <!-- 导航栏 -->
    <nav class="navbar">
      <div class="container">
        <div class="nav-content">
          <div class="nav-brand">
            <router-link to="/" class="brand-link">
              <div class="brand-icon">🛡️</div>
              <span class="brand-text">AWVS 漏洞管理</span>
            </router-link>
          </div>
          
          <div class="nav-links">
            <router-link to="/" class="nav-link" active-class="active">
              仪表盘
            </router-link>
            <router-link to="/vulnerabilities" class="nav-link" active-class="active">
              漏洞列表
            </router-link>
            <router-link to="/vulnerabilities-by-domain" class="nav-link" active-class="active">
              按域名分类
            </router-link>
          </div>
          
          <div class="nav-actions">
            <button @click="syncData" class="btn btn-secondary" :disabled="isLoading">
              <RefreshCw :size="16" :class="{ 'animate-spin': isLoading }" />
              同步
            </button>
          </div>
        </div>
      </div>
    </nav>

    <!-- 主要内容区域 -->
    <main class="main-content">
      <div class="container">
        <!-- 错误提示 -->
        <div v-if="hasError" class="error-banner">
          <div class="error-content">
            <AlertCircle :size="20" />
            <span>{{ error }}</span>
            <button @click="clearError" class="error-close">
              <X :size="16" />
            </button>
          </div>
        </div>

        <!-- 路由视图 -->
        <router-view />
      </div>
    </main>

    <!-- 页脚 -->
    <footer class="footer">
      <div class="container">
        <div class="footer-content">
          <p>&copy; 2024 AWVS 漏洞管理系统. 基于 Vue 3 + FastAPI 构建</p>
          <div class="footer-links">
            <a href="#" class="footer-link">文档</a>
            <a href="#" class="footer-link">支持</a>
            <a href="#" class="footer-link">关于</a>
          </div>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup>
import { onMounted } from 'vue'
import { RefreshCw, AlertCircle, X } from 'lucide-vue-next'
import { useVulnerabilityStore } from '@/stores/vulnerability'
import { storeToRefs } from 'pinia'

const vulnerabilityStore = useVulnerabilityStore()
const { error, isLoading, hasError } = storeToRefs(vulnerabilityStore)
const { syncData, clearError } = vulnerabilityStore

onMounted(() => {
  // 应用启动时获取初始数据
  vulnerabilityStore.fetchVulnerabilities()
  vulnerabilityStore.fetchStats()
})
</script>

<style scoped>
/* 导航栏样式 */
.navbar {
  background: var(--bg-primary);
  border-bottom: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);
  position: sticky;
  top: 0;
  z-index: 100;
}

.nav-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md) 0;
}

.nav-brand .brand-link {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  text-decoration: none;
  color: var(--text-primary);
  font-weight: 600;
  font-size: var(--font-size-lg);
}

.brand-icon {
  font-size: var(--font-size-2xl);
}

.nav-links {
  display: flex;
  gap: var(--spacing-lg);
}

.nav-link {
  text-decoration: none;
  color: var(--text-secondary);
  font-weight: 500;
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-md);
  transition: all 0.2s ease;
}

.nav-link:hover {
  color: var(--primary-color);
  background: var(--bg-secondary);
}

.nav-link.active {
  color: var(--primary-color);
  background: var(--bg-secondary);
}

.nav-actions {
  display: flex;
  gap: var(--spacing-sm);
}

/* 主要内容区域 */
.main-content {
  min-height: calc(100vh - 140px);
  padding: var(--spacing-xl) 0;
}

/* 错误提示样式 */
.error-banner {
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.error-content {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  color: #dc2626;
}

.error-close {
  margin-left: auto;
  background: none;
  border: none;
  color: #dc2626;
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: var(--radius-sm);
  transition: background 0.2s ease;
}

.error-close:hover {
  background: #fecaca;
}

/* 页脚样式 */
.footer {
  background: var(--bg-tertiary);
  border-top: 1px solid var(--border-color);
  padding: var(--spacing-lg) 0;
  margin-top: auto;
}

.footer-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
}

.footer-links {
  display: flex;
  gap: var(--spacing-lg);
}

.footer-link {
  color: var(--text-secondary);
  text-decoration: none;
  transition: color 0.2s ease;
}

.footer-link:hover {
  color: var(--primary-color);
}

/* 动画 */
.animate-spin {
  animation: spin 1s linear infinite;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .nav-content {
    flex-direction: column;
    gap: var(--spacing-md);
  }
  
  .nav-links {
    order: 2;
  }
  
  .nav-actions {
    order: 1;
  }
  
  .footer-content {
    flex-direction: column;
    gap: var(--spacing-sm);
    text-align: center;
  }
}
</style>
