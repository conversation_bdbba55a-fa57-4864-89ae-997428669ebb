# AWVS 漏洞管理系统

一个现代化的漏洞管理系统，用于从AWVS实例获取和展示高危及严重级别的漏洞数据。

## 技术栈

- **前端**: Vue 3 + Composition API + Vite
- **后端**: Python FastAPI
- **UI设计**: 参考 uiverse.io 现代化组件样式

## 项目结构

```
awvs-vulnerability-manager/
├── frontend/                 # Vue 3 前端项目
│   ├── src/
│   │   ├── components/      # Vue 组件
│   │   ├── views/          # 页面视图
│   │   ├── services/       # API 服务
│   │   ├── styles/         # 样式文件
│   │   └── main.js         # 入口文件
│   ├── package.json
│   └── vite.config.js
├── backend/                 # Python FastAPI 后端
│   ├── app/
│   │   ├── api/            # API 路由
│   │   ├── services/       # 业务逻辑
│   │   ├── models/         # 数据模型
│   │   └── main.py         # FastAPI 应用入口
│   ├── requirements.txt
│   └── config.py
└── README.md
```

## 功能特性

- 🔍 从AWVS实例获取漏洞数据
- 🎯 筛选高危和严重级别漏洞
- 📊 现代化卡片式漏洞展示
- 📱 响应式设计，支持移动端
- ⚡ 实时数据更新
- 🎨 基于uiverse.io的现代UI设计

## 快速开始

### 后端启动

```bash
cd backend
pip install -r requirements.txt
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### 前端启动

```bash
cd frontend
npm install
npm run dev
```

## API配置

- **AWVS实例**: https://*************:13443/
- **API基础URL**: https://*************:13443/api/v1/
- **API密钥**: 配置在后端环境变量中

## 访问地址

- 前端应用: http://localhost:5173
- 后端API: http://localhost:8000
- API文档: http://localhost:8000/docs

## 环境要求

- Node.js 16+
- Python 3.8+
- 现代浏览器支持

## 详细安装和运行说明

### 环境要求

- **Node.js**: 16.0+ (推荐使用 18.x 或更高版本)
- **Python**: 3.8+ (推荐使用 3.9 或更高版本)
- **npm**: 8.0+ 或 **yarn**: 1.22+
- **现代浏览器**: Chrome 90+, Firefox 88+, Safari 14+

### 快速启动 (推荐)

#### Windows 用户
```bash
# 启动后端 (在新的命令行窗口中)
start-backend.bat

# 启动前端 (在另一个新的命令行窗口中)
start-frontend.bat
```

#### Linux/macOS 用户
```bash
# 给脚本执行权限
chmod +x start-backend.sh start-frontend.sh

# 启动后端 (在新的终端中)
./start-backend.sh

# 启动前端 (在另一个新的终端中)
./start-frontend.sh
```

### 手动安装和启动

#### 1. 后端设置

```bash
# 进入后端目录
cd backend

# 创建虚拟环境 (推荐)
python -m venv venv

# 激活虚拟环境
# Windows:
venv\Scripts\activate
# Linux/macOS:
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt

# 复制环境配置文件
cp .env.example .env

# 启动后端服务
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

#### 2. 前端设置

```bash
# 进入前端目录
cd frontend

# 安装依赖
npm install
# 或使用 yarn
yarn install

# 复制环境配置文件
cp .env.example .env

# 启动开发服务器
npm run dev
# 或使用 yarn
yarn dev
```

### 访问应用

- **前端应用**: http://localhost:5173
- **后端API**: http://localhost:8000
- **API文档**: http://localhost:8000/docs
- **ReDoc文档**: http://localhost:8000/redoc

### 配置说明

#### 后端配置 (backend/.env)
```env
# AWVS API 配置
AWVS_BASE_URL=https://*************:13443/api/v1
AWVS_API_KEY=1986ad8c0a5b3df4d7028d5f3c06e936ce22a0e874df645d383dc09e3f004d50a

# 服务器配置
HOST=0.0.0.0
PORT=8000
DEBUG=True

# SSL配置 (AWVS使用自签名证书)
VERIFY_SSL=False
```

#### 前端配置 (frontend/.env)
```env
# API配置
VITE_API_BASE_URL=http://localhost:8000

# 应用配置
VITE_APP_TITLE=AWVS 漏洞管理系统
VITE_APP_VERSION=1.0.0
```

### 功能特性

#### 🔍 漏洞数据获取
- 自动从AWVS实例获取漏洞数据
- 支持多种API端点尝试
- 包含模拟数据用于演示

#### 🎯 智能过滤
- 按严重程度筛选 (严重、高危、中危、低危)
- 按状态筛选 (未修复、已修复、已接受、已忽略)
- 实时搜索和分页

#### 📊 数据展示
- 现代化卡片式设计
- 响应式布局适配移动端
- 实时统计信息展示

#### 🎨 UI设计
- 参考 uiverse.io 的现代化组件样式
- 支持深色模式
- 流畅的动画和交互效果

### 故障排除

#### 常见问题

1. **后端启动失败**
   ```bash
   # 检查Python版本
   python --version

   # 检查依赖安装
   pip list

   # 重新安装依赖
   pip install -r requirements.txt --force-reinstall
   ```

2. **前端启动失败**
   ```bash
   # 检查Node.js版本
   node --version
   npm --version

   # 清除缓存并重新安装
   npm cache clean --force
   rm -rf node_modules package-lock.json
   npm install
   ```

3. **AWVS连接失败**
   - 检查AWVS实例是否可访问
   - 验证API密钥是否正确
   - 确认SSL证书设置 (VERIFY_SSL=False)

4. **CORS错误**
   - 确保后端CORS配置正确
   - 检查前端API基础URL设置

#### 日志查看

- **后端日志**: 在后端启动的终端中查看
- **前端日志**: 在浏览器开发者工具的Console中查看
- **网络请求**: 在浏览器开发者工具的Network标签中查看

### 开发说明

1. **后端架构**
   - FastAPI框架提供RESTful API
   - 异步HTTP客户端连接AWVS
   - Pydantic数据验证和序列化
   - 完整的错误处理和日志记录

2. **前端架构**
   - Vue 3 Composition API
   - Pinia状态管理
   - Vue Router路由管理
   - Axios HTTP客户端

3. **代码规范**
   - 后端遵循PEP 8规范
   - 前端使用ESLint和Prettier
   - 组件化开发和模块化设计

4. **安全考虑**
   - API密钥安全存储
   - CORS跨域保护
   - 输入验证和错误处理
