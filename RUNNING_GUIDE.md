# AWVS 漏洞管理系统 - 运行指南

## 🚀 快速启动

### 前提条件
- ✅ Node.js 16.0+ (推荐 18.x+)
- ✅ Python 3.8+ (推荐 3.9+)
- ✅ 现代浏览器 (Chrome 90+, Firefox 88+, Safari 14+)

### 一键启动 (推荐)

#### Windows 用户
```bash
# 方法1: 使用批处理脚本
start-backend.bat    # 启动后端 (新窗口)
start-frontend.bat   # 启动前端 (新窗口)

# 方法2: 手动启动
# 终端1 - 启动后端
cd backend
python -m uvicorn app.main:app --host 0.0.0.0 --port 8000

# 终端2 - 启动前端  
cd frontend
npm run dev
```

#### Linux/macOS 用户
```bash
# 方法1: 使用shell脚本
chmod +x start-backend.sh start-frontend.sh
./start-backend.sh   # 启动后端
./start-frontend.sh  # 启动前端

# 方法2: 手动启动
# 终端1 - 启动后端
cd backend
python -m uvicorn app.main:app --host 0.0.0.0 --port 8000

# 终端2 - 启动前端
cd frontend
npm run dev
```

## 📋 详细步骤

### 1. 后端设置

```bash
# 进入后端目录
cd backend

# 安装Python依赖
pip install -r requirements.txt

# 配置环境变量 (可选)
cp .env.example .env
# 编辑 .env 文件设置AWVS连接信息

# 启动后端服务
python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### 2. 前端设置

```bash
# 进入前端目录
cd frontend

# 安装Node.js依赖
npm install

# 配置环境变量 (可选)
cp .env.example .env
# 编辑 .env 文件设置API地址

# 启动前端开发服务器
npm run dev
```

## 🌐 访问地址

启动成功后，可以通过以下地址访问：

- **🎯 前端应用**: http://localhost:5173
- **🔧 后端API**: http://localhost:8000
- **📚 API文档**: http://localhost:8000/docs
- **📖 ReDoc文档**: http://localhost:8000/redoc

## ✅ 验证运行状态

### 检查后端状态
```bash
# 健康检查
curl http://localhost:8000/health

# 获取漏洞数据
curl http://localhost:8000/api/vulnerabilities
```

### 检查前端状态
- 打开浏览器访问 http://localhost:5173
- 应该看到漏洞管理系统的仪表盘界面
- 检查浏览器控制台是否有错误信息

## 🔧 配置说明

### 后端配置 (backend/.env)
```env
# AWVS API配置
AWVS_BASE_URL=https://*************:13443/api/v1
AWVS_API_KEY=your_api_key_here

# 服务器配置
HOST=0.0.0.0
PORT=8000
DEBUG=True

# SSL配置
VERIFY_SSL=False
```

### 前端配置 (frontend/.env)
```env
# API配置
VITE_API_BASE_URL=http://localhost:8000

# 应用配置
VITE_APP_TITLE=AWVS 漏洞管理系统
VITE_APP_VERSION=1.0.0
```

## 🎯 功能验证

### 1. 仪表盘功能
- ✅ 漏洞统计卡片显示
- ✅ 最新漏洞列表
- ✅ 快速操作按钮

### 2. 漏洞列表功能
- ✅ 漏洞数据展示
- ✅ 严重程度筛选
- ✅ 状态筛选
- ✅ 分页加载

### 3. 漏洞详情功能
- ✅ 详细信息展示
- ✅ 技术信息
- ✅ 修复建议

## 🐛 故障排除

### 常见问题

#### 1. 后端启动失败
```bash
# 检查Python版本
python --version

# 检查依赖
pip list | grep fastapi

# 重新安装依赖
pip install -r requirements.txt --force-reinstall
```

#### 2. 前端启动失败
```bash
# 检查Node.js版本
node --version
npm --version

# 清除缓存重新安装
npm cache clean --force
rm -rf node_modules package-lock.json
npm install
```

#### 3. API连接失败
- 确保后端服务正在运行 (http://localhost:8000/health)
- 检查防火墙设置
- 验证CORS配置

#### 4. AWVS连接问题
- 检查AWVS实例是否可访问
- 验证API密钥是否正确
- 确认SSL证书设置

## 📊 性能优化

### 开发环境
- 后端: 使用 `--reload` 参数自动重载
- 前端: Vite热更新自动生效

### 生产环境
```bash
# 构建前端
cd frontend
npm run build

# 生产模式启动后端
cd backend
python -m uvicorn app.main:app --host 0.0.0.0 --port 8000
```

## 🔒 安全注意事项

1. **API密钥安全**: 不要在代码中硬编码API密钥
2. **CORS配置**: 生产环境中限制允许的源
3. **SSL证书**: 生产环境中使用有效的SSL证书
4. **防火墙**: 适当配置防火墙规则

## 📞 技术支持

如果遇到问题，请检查：
1. 📋 本文档的故障排除部分
2. 🔍 浏览器开发者工具的控制台错误
3. 📝 后端服务的日志输出
4. 🌐 网络连接状态

---

**🎉 恭喜！您的AWVS漏洞管理系统已经成功运行！**
