{"name": "awvs-vulnerability-frontend", "version": "1.0.0", "description": "AWVS漏洞管理系统前端", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore"}, "dependencies": {"vue": "^3.3.8", "axios": "^1.6.2", "vue-router": "^4.2.5", "pinia": "^2.1.7", "@vueuse/core": "^10.5.0", "lucide-vue-next": "^0.294.0"}, "devDependencies": {"@vitejs/plugin-vue": "^4.5.0", "vite": "^5.0.0", "eslint": "^8.53.0", "eslint-plugin-vue": "^9.18.1", "@vue/eslint-config-prettier": "^8.0.0", "prettier": "^3.1.0"}}