<template>
  <div class="dashboard">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">漏洞管理仪表盘</h1>
      <p class="page-subtitle">实时监控严重级别的安全漏洞</p>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-grid">
      <div class="stat-card total">
        <div class="stat-icon">
          <AlertTriangle :size="24" />
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ stats.total_count }}</div>
          <div class="stat-label">严重漏洞总数</div>
        </div>
      </div>

      <div class="stat-card open">
        <div class="stat-icon">
          <Clock :size="24" />
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ stats.open_count }}</div>
          <div class="stat-label">待修复</div>
        </div>
      </div>
    </div>

    <!-- 最新漏洞列表 -->
    <div class="recent-vulnerabilities">
      <div class="section-header">
        <h2 class="section-title">最新发现的漏洞</h2>
        <router-link to="/vulnerabilities" class="btn btn-primary">
          查看全部
          <ArrowRight :size="16" />
        </router-link>
      </div>

      <!-- 加载状态 -->
      <div v-if="isLoading" class="loading-container">
        <div class="loading-spinner"></div>
        <p>正在加载漏洞数据...</p>
      </div>

      <!-- 漏洞卡片列表 -->
      <div v-else-if="hasVulnerabilities" class="vulnerability-grid">
        <div 
          v-for="vulnerability in recentVulnerabilities" 
          :key="vulnerability.id"
          class="vulnerability-card"
          @click="goToDetail(vulnerability.id)"
        >
          <div class="vulnerability-header">
            <div class="vulnerability-title">{{ vulnerability.name }}</div>
            <div :class="['severity-badge', getSeverityClass(vulnerability.severity)]">
              {{ getSeverityLabel(vulnerability.severity) }}
            </div>
          </div>
          
          <div class="vulnerability-meta">
            <div class="meta-item">
              <Globe :size="14" />
              <span>{{ vulnerability.target_url || '未知目标' }}</span>
            </div>
            <div class="meta-item">
              <Calendar :size="14" />
              <span>{{ formatDate(vulnerability.discovered_at) }}</span>
            </div>
          </div>

          <div v-if="vulnerability.description" class="vulnerability-description">
            {{ truncateText(vulnerability.description, 100) }}
          </div>

          <div class="vulnerability-footer">
            <div v-if="vulnerability.cvss_score" class="cvss-score">
              CVSS: {{ vulnerability.cvss_score }}
            </div>
            <div class="view-detail">
              查看详情 <ArrowRight :size="14" />
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else class="empty-state">
        <Shield :size="48" />
        <h3>暂无漏洞数据</h3>
        <p>系统中暂时没有发现高危或严重级别的漏洞</p>
        <button @click="refreshData" class="btn btn-primary">
          <RefreshCw :size="16" />
          刷新数据
        </button>
      </div>
    </div>

    <!-- 快速操作 -->
    <div class="quick-actions">
      <div class="section-header">
        <h2 class="section-title">快速操作</h2>
      </div>
      
      <div class="action-grid">
        <button @click="refreshData" class="action-card" :disabled="isLoading">
          <RefreshCw :size="24" :class="{ 'animate-spin': isLoading }" />
          <span>刷新数据</span>
        </button>
        
        <router-link to="/vulnerabilities?severity=critical" class="action-card">
          <AlertTriangle :size="24" />
          <span>查看严重漏洞</span>
        </router-link>
        

        <button @click="exportData" class="action-card">
          <Download :size="24" />
          <span>导出报告</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { 
  AlertTriangle, 
  AlertCircle, 
  Shield, 
  Clock, 
  ArrowRight, 
  Globe, 
  Calendar,
  RefreshCw,
  Download
} from 'lucide-vue-next'
import { useVulnerabilityStore } from '@/stores/vulnerability'
import { storeToRefs } from 'pinia'

const router = useRouter()
const vulnerabilityStore = useVulnerabilityStore()

const { 
  vulnerabilities, 
  stats, 
  isLoading, 
  hasVulnerabilities 
} = storeToRefs(vulnerabilityStore)

const {
  fetchVulnerabilities,
  getSeverityLabel,
  getSeverityClass,
  formatDate
} = vulnerabilityStore

// 刷新函数，只重新获取当前页面数据
async function refreshData() {
  await fetchVulnerabilities()
}

// 计算属性
const recentVulnerabilities = computed(() => {
  return vulnerabilities.value
    .slice()
    .sort((a, b) => new Date(b.discovered_at) - new Date(a.discovered_at))
    .slice(0, 6)
})

// 方法
function goToDetail(id) {
  // 在新标签页中打开漏洞详情
  const url = router.resolve(`/vulnerabilities/${id}`).href
  window.open(url, '_blank')
}

function truncateText(text, maxLength) {
  if (!text) return ''
  return text.length > maxLength ? text.substring(0, maxLength) + '...' : text
}

function exportData() {
  // 这里可以实现导出功能
  alert('导出功能开发中...')
}

onMounted(() => {
  // 组件挂载时确保数据是最新的
  if (!hasVulnerabilities.value) {
    vulnerabilityStore.fetchVulnerabilities()
  }
})
</script>

<style scoped>
.dashboard {
  max-width: 1200px;
  margin: 0 auto;
}

/* 页面标题 */
.page-header {
  text-align: center;
  margin-bottom: var(--spacing-2xl);
}

.page-title {
  font-size: var(--font-size-3xl);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.page-subtitle {
  font-size: var(--font-size-lg);
  color: var(--text-secondary);
}

/* 统计卡片网格 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-2xl);
}

.stat-card {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.stat-card.critical {
  border-left: 4px solid var(--critical-color);
}

.stat-card.high {
  border-left: 4px solid var(--high-color);
}

.stat-card.total {
  border-left: 4px solid var(--primary-color);
}

.stat-card.open {
  border-left: 4px solid var(--accent-color);
}

.stat-icon {
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  background: var(--bg-secondary);
  color: var(--text-secondary);
}

.stat-number {
  font-size: var(--font-size-2xl);
  font-weight: 700;
  color: var(--text-primary);
}

.stat-label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin-top: var(--spacing-xs);
}

/* 最新漏洞部分 */
.recent-vulnerabilities {
  margin-bottom: var(--spacing-2xl);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
}

.section-title {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--text-primary);
}

/* 漏洞卡片网格 */
.vulnerability-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: var(--spacing-lg);
}

.vulnerability-card {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-color);
  cursor: pointer;
  transition: all 0.3s ease;
}

.vulnerability-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  border-color: var(--primary-color);
}

.vulnerability-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-md);
}

.vulnerability-title {
  font-weight: 600;
  color: var(--text-primary);
  flex: 1;
  margin-right: var(--spacing-sm);
}

.vulnerability-meta {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
  margin-bottom: var(--spacing-md);
}

.meta-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.vulnerability-description {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  line-height: 1.5;
  margin-bottom: var(--spacing-md);
}

.vulnerability-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.cvss-score {
  font-size: var(--font-size-sm);
  font-weight: 600;
  color: var(--text-primary);
}

.view-detail {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: var(--font-size-sm);
  color: var(--primary-color);
  font-weight: 500;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-2xl);
  color: var(--text-secondary);
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: var(--spacing-2xl);
  color: var(--text-secondary);
}

.empty-state h3 {
  margin: var(--spacing-md) 0 var(--spacing-sm);
  color: var(--text-primary);
}

/* 快速操作 */
.action-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-md);
}

.action-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-lg);
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  text-decoration: none;
  color: var(--text-primary);
  transition: all 0.3s ease;
  cursor: pointer;
}

.action-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.action-card:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .vulnerability-grid {
    grid-template-columns: 1fr;
  }
  
  .section-header {
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: stretch;
  }
  
  .action-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>
