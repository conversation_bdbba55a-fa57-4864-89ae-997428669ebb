<template>
  <div class="vulnerability-detail">
    <!-- 返回按钮 -->
    <div class="back-navigation">
      <button @click="goBack" class="btn btn-secondary">
        <ArrowLeft :size="16" />
        返回列表
      </button>
    </div>

    <!-- 加载状态 -->
    <div v-if="isLoading" class="loading-container">
      <div class="loading-spinner"></div>
      <p>正在加载漏洞详情...</p>
    </div>

    <!-- 漏洞详情 -->
    <div v-else-if="vulnerability" class="detail-container">
      <!-- 漏洞标题和基本信息 -->
      <div class="detail-header">
        <div class="header-main">
          <h1 class="vulnerability-title">{{ vulnerability.name }}</h1>
          <div class="header-badges">
            <div :class="['severity-badge', getSeverityClass(vulnerability.severity)]">
              {{ getSeverityLabel(vulnerability.severity) }}
            </div>
            <div class="status-badge">
              {{ getStatusLabel(vulnerability.status) }}
            </div>
          </div>
        </div>
        
        <div class="header-meta">
          <div class="meta-grid">
            <div class="meta-item">
              <Globe :size="16" />
              <div class="meta-content">
                <span class="meta-label">目标URL</span>
                <span class="meta-value">{{ vulnerability.target_url || '未知' }}</span>
              </div>
            </div>
            
            <div class="meta-item">
              <Calendar :size="16" />
              <div class="meta-content">
                <span class="meta-label">发现时间</span>
                <span class="meta-value">{{ formatDate(vulnerability.discovered_at) }}</span>
              </div>
            </div>
            
            <div v-if="vulnerability.cvss_score" class="meta-item">
              <AlertTriangle :size="16" />
              <div class="meta-content">
                <span class="meta-label">CVSS评分</span>
                <span class="meta-value">{{ vulnerability.cvss_score }}</span>
              </div>
            </div>
            
            <div v-if="vulnerability.cve_id" class="meta-item">
              <Shield :size="16" />
              <div class="meta-content">
                <span class="meta-label">CVE编号</span>
                <span class="meta-value">{{ vulnerability.cve_id }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 详细信息卡片 -->
      <div class="detail-cards">
        <!-- 漏洞描述 -->
        <div class="detail-card">
          <div class="card-header">
            <h2 class="card-title">
              <FileText :size="20" />
              漏洞描述
            </h2>
          </div>
          <div class="card-body">
            <p v-if="vulnerability.description" class="description-text">
              {{ vulnerability.description }}
            </p>
            <p v-else class="no-data">暂无详细描述</p>
          </div>
        </div>

        <!-- 修复建议 -->
        <div v-if="vulnerability.recommendation" class="detail-card">
          <div class="card-header">
            <h2 class="card-title">
              <Wrench :size="20" />
              修复建议
            </h2>
          </div>
          <div class="card-body">
            <p class="recommendation-text">{{ vulnerability.recommendation }}</p>
          </div>
        </div>

        <!-- 技术信息 -->
        <div class="detail-card">
          <div class="card-header">
            <h2 class="card-title">
              <Info :size="20" />
              技术信息
            </h2>
          </div>
          <div class="card-body">
            <div class="info-grid">
              <div class="info-item">
                <span class="info-label">漏洞ID:</span>
                <span class="info-value">{{ vulnerability.id }}</span>
              </div>
              
              <div v-if="vulnerability.category" class="info-item">
                <span class="info-label">漏洞类别:</span>
                <span class="info-value">{{ vulnerability.category }}</span>
              </div>
              
              <div class="info-item">
                <span class="info-label">严重程度:</span>
                <span class="info-value">{{ getSeverityLabel(vulnerability.severity) }}</span>
              </div>
              
              <div class="info-item">
                <span class="info-label">当前状态:</span>
                <span class="info-value">{{ getStatusLabel(vulnerability.status) }}</span>
              </div>
              
              <div v-if="vulnerability.target_name" class="info-item">
                <span class="info-label">目标名称:</span>
                <span class="info-value">{{ vulnerability.target_name }}</span>
              </div>
              
              <div v-if="vulnerability.last_seen" class="info-item">
                <span class="info-label">最后发现:</span>
                <span class="info-value">{{ formatDate(vulnerability.last_seen) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- HTTP Request信息 -->
      <div v-if="vulnerability.http_request" class="detail-card">
        <div class="card-header">
          <h2 class="card-title">
            <Code :size="20" />
            HTTP Request
          </h2>
          <button
            @click="copyHttpRequest"
            class="copy-button"
            :class="{ 'copied': httpRequestCopied }"
            :title="httpRequestCopied ? '已复制' : '复制HTTP Request'"
          >
            <Check v-if="httpRequestCopied" :size="16" />
            <Copy v-else :size="16" />
          </button>
        </div>
        <div class="card-body">
          <div class="http-request-container">
            <pre class="http-request-content">{{ formatHttpRequest(vulnerability.http_request) }}</pre>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="detail-actions">
        <button @click="goBack" class="btn btn-secondary">
          <ArrowLeft :size="16" />
          返回列表
        </button>
        
        <button @click="exportVulnerability" class="btn btn-primary">
          <Download :size="16" />
          导出报告
        </button>
      </div>
    </div>

    <!-- 错误状态 -->
    <div v-else class="error-state">
      <AlertCircle :size="64" />
      <h3>漏洞不存在</h3>
      <p>未找到指定的漏洞信息，可能已被删除或ID不正确</p>
      <button @click="goBack" class="btn btn-primary">
        返回列表
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import {
  ArrowLeft,
  Globe,
  Calendar,
  AlertTriangle,
  Shield,
  FileText,
  Wrench,
  Info,
  Download,
  AlertCircle,
  Code,
  Copy,
  Check
} from 'lucide-vue-next'
import { useVulnerabilityStore } from '@/stores/vulnerability'
import { storeToRefs } from 'pinia'
import { vulnerabilityAPI } from '@/services/api'

const router = useRouter()
const route = useRoute()
const vulnerabilityStore = useVulnerabilityStore()

const {
  getSeverityLabel,
  getSeverityClass,
  getStatusLabel,
  formatDate
} = vulnerabilityStore

const vulnerability = ref(null)
const isLoading = ref(false)
const httpRequestCopied = ref(false)

// 计算属性
const vulnerabilityId = computed(() => route.params.id)

// 方法
function goBack() {
  router.push('/vulnerabilities')
}

function exportVulnerability() {
  // 这里可以实现导出功能
  alert('导出功能开发中...')
}

function formatHttpRequest(httpRequest) {
  if (!httpRequest) return ''

  // 处理各种换行符格式
  return httpRequest
    .replace(/\\r\\n/g, '\n')  // 处理转义的\r\n
    .replace(/\\n/g, '\n')     // 处理转义的\n
    .replace(/\r\n/g, '\n')    // 处理实际的\r\n
    .replace(/\r/g, '\n')      // 处理单独的\r
}

async function copyHttpRequest() {
  if (!vulnerability.value?.http_request) return

  try {
    const formattedRequest = formatHttpRequest(vulnerability.value.http_request)
    await navigator.clipboard.writeText(formattedRequest)

    // 显示复制成功状态
    httpRequestCopied.value = true

    // 2秒后重置状态
    setTimeout(() => {
      httpRequestCopied.value = false
    }, 2000)
  } catch (error) {
    console.error('Failed to copy HTTP request:', error)
    // 如果clipboard API不可用，使用fallback方法
    fallbackCopyTextToClipboard(formatHttpRequest(vulnerability.value.http_request))
  }
}

function fallbackCopyTextToClipboard(text) {
  const textArea = document.createElement('textarea')
  textArea.value = text
  textArea.style.position = 'fixed'
  textArea.style.left = '-999999px'
  textArea.style.top = '-999999px'
  document.body.appendChild(textArea)
  textArea.focus()
  textArea.select()

  try {
    document.execCommand('copy')
    httpRequestCopied.value = true
    setTimeout(() => {
      httpRequestCopied.value = false
    }, 2000)
  } catch (err) {
    console.error('Fallback: Oops, unable to copy', err)
  }

  document.body.removeChild(textArea)
}

async function loadVulnerability() {
  try {
    isLoading.value = true

    // 直接从API获取漏洞详情，确保获取到完整的数据包括HTTP Request
    const vulnData = await vulnerabilityAPI.getVulnerabilityDetail(vulnerabilityId.value)
    vulnerability.value = vulnData

    console.log('Loaded vulnerability detail:', vulnData)
  } catch (error) {
    console.error('Error loading vulnerability detail:', error)
    vulnerability.value = null
  } finally {
    isLoading.value = false
  }
}

onMounted(() => {
  loadVulnerability()
})
</script>

<style scoped>
.vulnerability-detail {
  max-width: 1000px;
  margin: 0 auto;
}

/* 返回导航 */
.back-navigation {
  margin-bottom: var(--spacing-lg);
}

/* 详情容器 */
.detail-container {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
}

/* 详情标题 */
.detail-header {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-md);
}

.header-main {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-lg);
  gap: var(--spacing-lg);
}

.vulnerability-title {
  font-size: var(--font-size-2xl);
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
  flex: 1;
}

.header-badges {
  display: flex;
  gap: var(--spacing-sm);
  flex-shrink: 0;
}

.status-badge {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  background: var(--bg-secondary);
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
}

/* 元信息网格 */
.meta-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-lg);
}

.meta-item {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-sm);
  padding: var(--spacing-md);
  background: var(--bg-secondary);
  border-radius: var(--radius-md);
}

.meta-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.meta-label {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  font-weight: 600;
}

.meta-value {
  font-size: var(--font-size-sm);
  color: var(--text-primary);
  font-weight: 500;
  word-break: break-all;
}

/* 详情卡片 */
.detail-cards {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.detail-card {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  background: var(--bg-secondary);
}

.card-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.card-body {
  padding: var(--spacing-lg);
}

.description-text,
.recommendation-text {
  font-size: var(--font-size-base);
  line-height: 1.6;
  color: var(--text-primary);
  margin: 0;
  white-space: pre-wrap;
}

.no-data {
  color: var(--text-secondary);
  font-style: italic;
  margin: 0;
}

/* 技术信息网格 */
.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-md);
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm);
  background: var(--bg-secondary);
  border-radius: var(--radius-sm);
}

.info-label {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  font-weight: 600;
}

.info-value {
  font-size: var(--font-size-sm);
  color: var(--text-primary);
  font-weight: 500;
  word-break: break-all;
}

/* 操作按钮 */
.detail-actions {
  display: flex;
  justify-content: space-between;
  gap: var(--spacing-md);
  padding: var(--spacing-lg);
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
}

/* 加载和错误状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-2xl);
  color: var(--text-secondary);
}

.error-state {
  text-align: center;
  padding: var(--spacing-2xl);
  color: var(--text-secondary);
}

.error-state h3 {
  margin: var(--spacing-md) 0 var(--spacing-sm);
  color: var(--text-primary);
}

/* HTTP Request样式 */
.http-request-container {
  background: var(--bg-secondary);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
  overflow: hidden;
}

.http-request-content {
  margin: 0;
  padding: var(--spacing-lg);
  background: #1e1e1e;
  color: #d4d4d4;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.5;
  overflow-x: auto;
  white-space: pre-wrap;
  word-break: break-all;
}

.http-request-content::-webkit-scrollbar {
  height: 8px;
}

.http-request-content::-webkit-scrollbar-track {
  background: #2d2d2d;
}

.http-request-content::-webkit-scrollbar-thumb {
  background: #555;
  border-radius: 4px;
}

.http-request-content::-webkit-scrollbar-thumb:hover {
  background: #777;
}

/* 复制按钮样式 */
.copy-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xs) var(--spacing-sm);
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: var(--font-size-sm);
  min-width: 40px;
  height: 32px;
}

.copy-button:hover {
  background: var(--bg-secondary);
  color: var(--text-primary);
  border-color: var(--primary-color);
}

.copy-button.copied {
  background: var(--success-color);
  color: white;
  border-color: var(--success-color);
}

.copy-button.copied:hover {
  background: var(--success-color);
  color: white;
  border-color: var(--success-color);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-main {
    flex-direction: column;
    gap: var(--spacing-md);
  }

  .header-badges {
    align-self: flex-start;
  }

  .meta-grid {
    grid-template-columns: 1fr;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }

  .detail-actions {
    flex-direction: column;
  }

  .vulnerability-title {
    font-size: var(--font-size-xl);
  }
}
</style>
