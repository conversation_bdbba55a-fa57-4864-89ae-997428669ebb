import os
from dotenv import load_dotenv

load_dotenv()

class Settings:
    # AWVS API 配置
    AWVS_BASE_URL = "https://*************:13443"
    AWVS_API_KEY = "1986ad8c0a5b3df4d7028d5f3c06e936ce22a0e874df645d383dc09e3f004d50a"
    
    # 服务器配置
    HOST = "0.0.0.0"
    PORT = 8000
    DEBUG = True
    
    # CORS 配置
    CORS_ORIGINS = [
        "http://localhost:5173",  # Vue dev server
        "http://localhost:5174",  # Vue dev server (备用端口)
        "http://localhost:5175",  # Vue dev server (备用端口)
        "http://localhost:3000",  # 备用端口
        "http://127.0.0.1:5173",
        "http://127.0.0.1:5174",
        "http://127.0.0.1:5175",
        "http://127.0.0.1:3000"
    ]
    
    # SSL 配置 (AWVS 使用自签名证书)
    VERIFY_SSL = False

settings = Settings()
