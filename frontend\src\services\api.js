import axios from 'axios'

// 创建axios实例
const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000',
  timeout: 300000, // 5分钟超时，用于获取大量漏洞数据
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    console.log('API Request:', config.method?.toUpperCase(), config.url)
    return config
  },
  (error) => {
    console.error('Request Error:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    console.log('API Response:', response.status, response.config.url)
    return response
  },
  (error) => {
    console.error('Response Error:', error.response?.status, error.response?.data || error.message)
    
    // 统一错误处理
    const errorMessage = error.response?.data?.detail || 
                        error.response?.data?.message || 
                        error.message || 
                        '网络请求失败'
    
    return Promise.reject(new Error(errorMessage))
  }
)

// 漏洞相关API
export const vulnerabilityAPI = {
  // 获取漏洞列表
  async getVulnerabilities(params = {}) {
    const defaultParams = {
      severity: ['critical', 'high'],
      status: ['open'],
      limit: 100,
      offset: 0
    }
    
    const response = await api.get('/api/vulnerabilities/', {
      params: { ...defaultParams, ...params }
    })
    return response.data
  },

  // 获取漏洞详情
  async getVulnerabilityDetail(id) {
    const response = await api.get(`/api/vulnerabilities/${id}`)
    return response.data
  },

  // 获取漏洞统计
  async getVulnerabilityStats() {
    const response = await api.get('/api/vulnerabilities/stats/summary')
    return response.data
  },

  // 手动同步漏洞数据（增量同步）
  async syncVulnerabilities() {
    const response = await api.post('/api/vulnerabilities/sync/incremental')
    return response.data
  },

  // 全量同步漏洞数据
  async fullSyncVulnerabilities() {
    const response = await api.post('/api/vulnerabilities/sync/full')
    return response.data
  }
}

// 系统相关API
export const systemAPI = {
  // 健康检查
  async healthCheck() {
    const response = await api.get('/health')
    return response.data
  },

  // 获取API信息
  async getApiInfo() {
    const response = await api.get('/')
    return response.data
  }
}

export default api
