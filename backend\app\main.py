from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import logging
import uvicorn
from contextlib import asynccontextmanager
from config import settings
from app.api.vulnerabilities import router as vulnerabilities_router
from app.api.domains import router as domains_router
from app.database import create_tables, close_db
from app.services.scheduler_service import scheduler_service

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# 应用生命周期管理
@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用启动和关闭时的生命周期管理"""
    # 启动时执行
    logger.info("Starting AWVS Vulnerability Management System...")

    try:
        # 创建数据库表
        await create_tables()
        logger.info("Database tables created/verified")

        # 启动定时任务调度器
        await scheduler_service.start()
        logger.info("Scheduler started")

        yield

    finally:
        # 关闭时执行
        logger.info("Shutting down AWVS Vulnerability Management System...")

        # 停止调度器
        await scheduler_service.stop()
        logger.info("Scheduler stopped")

        # 关闭数据库连接
        await close_db()
        logger.info("Database connections closed")

# 创建FastAPI应用
app = FastAPI(
    title="AWVS 漏洞管理系统 API",
    description="用于从AWVS获取和管理漏洞数据的API服务",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

# 注册路由
app.include_router(vulnerabilities_router)
app.include_router(domains_router)

@app.get("/")
async def root():
    """根路径，返回API信息"""
    return {
        "message": "AWVS 漏洞管理系统 API",
        "version": "1.0.0",
        "docs": "/docs",
        "redoc": "/redoc"
    }

@app.get("/health")
async def health_check():
    """健康检查端点"""
    return {
        "status": "healthy",
        "message": "API service is running"
    }

@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """全局异常处理"""
    logger.error(f"Global exception: {str(exc)}")
    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal server error",
            "message": str(exc) if settings.DEBUG else "An error occurred"
        }
    )

if __name__ == "__main__":
    uvicorn.run(
        "app.main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level="info"
    )
