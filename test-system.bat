@echo off
echo ========================================
echo AWVS 漏洞管理系统 - 功能测试
echo ========================================
echo.

echo 1. 测试后端健康状态...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:8000/health' -Method GET -TimeoutSec 5; Write-Host '✅ 后端服务正常' -ForegroundColor Green; Write-Host $response.Content } catch { Write-Host '❌ 后端服务异常' -ForegroundColor Red; Write-Host $_.Exception.Message }"

echo.
echo 2. 测试漏洞数据API...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:8000/api/vulnerabilities' -Method GET -TimeoutSec 10; Write-Host '✅ 漏洞数据API正常' -ForegroundColor Green; $data = $response.Content | ConvertFrom-Json; Write-Host ('返回漏洞数量: ' + $data.vulnerabilities.Count) } catch { Write-Host '❌ 漏洞数据API异常' -ForegroundColor Red; Write-Host $_.Exception.Message }"

echo.
echo 3. 测试前端服务...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:5173' -Method GET -TimeoutSec 5; Write-Host '✅ 前端服务正常' -ForegroundColor Green } catch { Write-Host '❌ 前端服务异常' -ForegroundColor Red; Write-Host $_.Exception.Message }"

echo.
echo 4. 测试API文档...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:8000/docs' -Method GET -TimeoutSec 5; Write-Host '✅ API文档正常' -ForegroundColor Green } catch { Write-Host '❌ API文档异常' -ForegroundColor Red; Write-Host $_.Exception.Message }"

echo.
echo ========================================
echo 测试完成！
echo.
echo 如果所有测试都通过，您可以访问：
echo 🎯 前端应用: http://localhost:5173
echo 🔧 后端API: http://localhost:8000
echo 📚 API文档: http://localhost:8000/docs
echo ========================================

pause
