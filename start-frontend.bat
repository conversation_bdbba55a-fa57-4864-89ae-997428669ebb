@echo off
title AWVS Frontend Server
echo Starting AWVS Vulnerability Management Frontend...
echo.

REM Check if frontend directory exists
if not exist "frontend" (
    echo Error: frontend directory not found!
    pause
    exit /b 1
)

cd frontend

echo Installing Node.js dependencies...
call npm install

echo.
echo Dependencies installed successfully!
echo.
echo Starting Vue development server...
echo You can access the application at: http://localhost:5173/
echo Press Ctrl+C to stop the server
echo.

call npm run dev

echo.
echo Development server stopped.
pause
