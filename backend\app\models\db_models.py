"""
数据库模型定义
"""
from sqlalchemy import Column, String, Text, DateTime, Float, Integer, Boolean, Index
from sqlalchemy.sql import func
from app.database import Base
from datetime import datetime
from typing import Optional

class VulnerabilityDB(Base):
    """漏洞数据库模型"""
    __tablename__ = "vulnerabilities"
    
    # 主键：使用AWVS的漏洞ID
    id = Column(String(255), primary_key=True, index=True)
    
    # 基本信息
    name = Column(String(500), nullable=False, index=True)
    description = Column(Text)
    severity = Column(String(50), nullable=False, index=True)
    status = Column(String(50), nullable=False, index=True)
    
    # 目标信息
    target_url = Column(String(1000), nullable=False, index=True)
    target_name = Column(String(500))
    
    # 时间信息
    discovered_at = Column(DateTime, nullable=False, index=True)
    last_seen = Column(DateTime)
    
    # 评分和分类
    cvss_score = Column(Float)
    cve_id = Column(String(50), index=True)
    category = Column(String(200))
    recommendation = Column(Text)
    
    # HTTP请求信息
    http_request = Column(Text)
    
    # 数据库管理字段
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False)
    
    # 添加索引以提高查询性能
    __table_args__ = (
        Index('idx_severity_status', 'severity', 'status'),
        Index('idx_target_url_severity', 'target_url', 'severity'),
        Index('idx_discovered_at_severity', 'discovered_at', 'severity'),
    )

class DomainProcessingStatus(Base):
    """域名处理状态模型"""
    __tablename__ = "domain_processing_status"
    
    # 主键：域名
    domain = Column(String(255), primary_key=True, index=True)
    
    # 处理状态
    is_processed = Column(Boolean, default=False, nullable=False)
    
    # 域名类型：main_domain 或 subdomain
    domain_type = Column(String(50), nullable=False, index=True)
    
    # 如果是子域名，记录其主域名
    parent_domain = Column(String(255), index=True)
    
    # 时间信息
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False)
    
    # 添加索引
    __table_args__ = (
        Index('idx_domain_type_processed', 'domain_type', 'is_processed'),
        Index('idx_parent_domain', 'parent_domain'),
    )

class SyncHistory(Base):
    """同步历史记录模型"""
    __tablename__ = "sync_history"
    
    # 主键
    id = Column(Integer, primary_key=True, autoincrement=True)
    
    # 同步信息
    sync_type = Column(String(50), nullable=False)  # 'full' 或 'incremental'
    start_time = Column(DateTime, nullable=False)
    end_time = Column(DateTime)
    
    # 同步结果
    status = Column(String(50), nullable=False)  # 'success', 'failed', 'running'
    total_vulnerabilities = Column(Integer, default=0)
    new_vulnerabilities = Column(Integer, default=0)
    updated_vulnerabilities = Column(Integer, default=0)
    
    # 错误信息
    error_message = Column(Text)
    
    # 最后同步的漏洞发现时间（用于增量同步）
    last_vulnerability_time = Column(DateTime)
    
    # 创建时间
    created_at = Column(DateTime, default=func.now(), nullable=False)
    
    # 添加索引
    __table_args__ = (
        Index('idx_sync_type_status', 'sync_type', 'status'),
        Index('idx_start_time', 'start_time'),
    )
