"""
数据库初始化脚本
"""
import asyncio
import logging
from app.database import create_tables, close_db

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

async def init_database():
    """初始化数据库"""
    try:
        logger.info("Initializing database...")
        
        # 创建数据库表
        await create_tables()
        logger.info("Database tables created successfully")
        
        # 关闭数据库连接
        await close_db()
        logger.info("Database initialization completed")
        
    except Exception as e:
        logger.error(f"Database initialization failed: {str(e)}")
        raise

if __name__ == "__main__":
    asyncio.run(init_database())
