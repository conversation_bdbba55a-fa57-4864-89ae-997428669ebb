import httpx
import asyncio
from typing import List, Dict, Any, Optional
from datetime import datetime, timezone
import logging
from config import settings
from app.models.vulnerability import Vulnerability, SeverityLevel, VulnerabilityStatus

logger = logging.getLogger(__name__)

class AWVSService:
    def __init__(self):
        self.base_url = settings.AWVS_BASE_URL
        self.api_key = settings.AWVS_API_KEY
        self.verify_ssl = settings.VERIFY_SSL
        
    async def _make_request(self, endpoint: str, method: str = "GET", params: Dict = None) -> Dict[str, Any]:
        """发送HTTP请求到AWVS API"""
        url = f"{self.base_url}/{endpoint.lstrip('/')}"
        headers = {
            "X-Auth": self.api_key,
            "Content-Type": "application/json"
        }
        
        try:
            async with httpx.AsyncClient(verify=self.verify_ssl, timeout=30.0) as client:
                if method.upper() == "GET":
                    response = await client.get(url, headers=headers, params=params or {})
                elif method.upper() == "POST":
                    response = await client.post(url, headers=headers, json=params or {})
                else:
                    raise ValueError(f"Unsupported HTTP method: {method}")
                
                response.raise_for_status()
                return response.json()
                
        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error {e.response.status_code}: {e.response.text}")
            raise Exception(f"AWVS API error: {e.response.status_code}")
        except httpx.RequestError as e:
            logger.error(f"Request error: {str(e)}")
            raise Exception(f"Failed to connect to AWVS: {str(e)}")
    
    async def get_vulnerabilities_page(self, cursor: str = None) -> Dict[str, Any]:
        """获取单页漏洞数据，支持cursor分页"""
        try:
            endpoint = "api/v1/vulnerabilities"
            params = {}
            if cursor:
                params['c'] = cursor

            logger.info(f"Fetching vulnerabilities page with cursor: {cursor[:50] if cursor else 'None'}...")
            data = await self._make_request(endpoint, params=params)

            if not data or not isinstance(data, dict):
                raise Exception("Invalid response format from AWVS API")

            vulnerabilities = data.get("vulnerabilities", [])
            pagination = data.get("pagination", {})

            logger.info(f"Found {len(vulnerabilities)} vulnerabilities on this page")
            logger.info(f"Total count from API: {pagination.get('count', 'unknown')}")

            return {
                "vulnerabilities": self._normalize_vulnerabilities(vulnerabilities),
                "pagination": pagination
            }

        except Exception as e:
            logger.error(f"Error fetching vulnerabilities page: {str(e)}")
            raise

    async def get_vulnerabilities(self, severity_filter: List[str] = None, limit: int = 1000, offset: int = 0) -> List[Dict[str, Any]]:
        """获取漏洞列表，保持向后兼容"""
        try:
            # 为了向后兼容，仍然支持这个方法，但只返回第一页
            page_data = await self.get_vulnerabilities_page()
            return page_data["vulnerabilities"]

        except Exception as e:
            logger.error(f"Error fetching vulnerabilities: {str(e)}")
            raise

    async def get_all_vulnerabilities(self, severity_filter: List[str] = None, max_pages: int = 500) -> List[Dict[str, Any]]:
        """获取所有漏洞，使用cursor分页"""
        try:
            all_vulnerabilities = []
            cursor = None
            page_count = 0

            logger.info("Starting to fetch all vulnerabilities with cursor pagination...")

            while page_count < max_pages:
                page_count += 1
                logger.info(f"Fetching page {page_count} with cursor: {cursor[:50] if cursor else 'None'}...")

                page_data = await self.get_vulnerabilities_page(cursor)
                page_vulnerabilities = page_data["vulnerabilities"]
                pagination = page_data["pagination"]

                if not page_vulnerabilities:
                    logger.info("No more vulnerabilities found, stopping pagination")
                    break

                all_vulnerabilities.extend(page_vulnerabilities)
                logger.info(f"Page {page_count}: {len(page_vulnerabilities)} vulnerabilities")
                logger.info(f"Total vulnerabilities so far: {len(all_vulnerabilities)}")
                logger.info(f"API reports total count: {pagination.get('count', 'unknown')}")

                # 获取下一页的cursor
                cursors = pagination.get("cursors", [])
                if len(cursors) > 1 and cursors[1]:
                    cursor = cursors[1]
                    logger.info(f"Next cursor: {cursor[:50]}...")
                else:
                    logger.info("No more pages available (no next cursor)")
                    break

            if page_count >= max_pages:
                logger.warning(f"Reached maximum page limit of {max_pages}")

            logger.info(f"Finished fetching vulnerabilities. Total pages: {page_count}, Total vulnerabilities: {len(all_vulnerabilities)}")
            return all_vulnerabilities

        except Exception as e:
            logger.error(f"Error fetching all vulnerabilities: {str(e)}")
            raise

    async def get_all_high_critical_vulnerabilities(self) -> List[Dict[str, Any]]:
        """获取所有高危和严重漏洞，带重试机制和延迟"""
        import asyncio

        try:
            all_vulnerabilities = []
            cursor = None
            page_count = 0
            consecutive_non_high_critical_pages = 0
            max_consecutive_non_high_critical = 10  # 连续10页没有高危/严重漏洞就停止

            logger.info("Starting to fetch all high and critical vulnerabilities...")

            while page_count < 500:  # 减少上限以避免超时
                page_count += 1
                logger.info(f"Fetching page {page_count} with cursor: {cursor[:50] if cursor else 'None'}...")

                # 添加重试机制
                max_retries = 3
                for retry in range(max_retries):
                    try:
                        page_data = await self.get_vulnerabilities_page(cursor)
                        break
                    except Exception as e:
                        if retry < max_retries - 1:
                            wait_time = (retry + 1) * 2  # 2, 4, 6秒
                            logger.warning(f"Request failed (attempt {retry + 1}/{max_retries}), retrying in {wait_time}s: {str(e)}")
                            await asyncio.sleep(wait_time)
                        else:
                            raise

                page_vulnerabilities = page_data["vulnerabilities"]
                pagination = page_data["pagination"]

                if not page_vulnerabilities:
                    logger.info("No more vulnerabilities found, stopping pagination")
                    break

                # 过滤当前页面的高危和严重漏洞
                high_critical_vulns = [
                    vuln for vuln in page_vulnerabilities
                    if vuln.get("severity") in ["critical", "high"]
                ]

                if high_critical_vulns:
                    all_vulnerabilities.extend(high_critical_vulns)
                    consecutive_non_high_critical_pages = 0
                    logger.info(f"Page {page_count}: {len(high_critical_vulns)}/{len(page_vulnerabilities)} high/critical vulnerabilities")
                else:
                    consecutive_non_high_critical_pages += 1
                    logger.info(f"Page {page_count}: No high/critical vulnerabilities found ({consecutive_non_high_critical_pages} consecutive pages)")

                    if consecutive_non_high_critical_pages >= max_consecutive_non_high_critical:
                        logger.info(f"Stopping after {consecutive_non_high_critical_pages} consecutive pages without high/critical vulnerabilities")
                        break

                logger.info(f"Total high/critical vulnerabilities so far: {len(all_vulnerabilities)}")
                logger.info(f"API reports total count: {pagination.get('count', 'unknown')}")

                # 获取下一页的cursor
                cursors = pagination.get("cursors", [])
                if len(cursors) > 1 and cursors[1]:
                    cursor = cursors[1]
                    logger.info(f"Next cursor: {cursor[:50]}...")
                else:
                    logger.info("No more pages available (no next cursor)")
                    break

                # 添加延迟以避免过于频繁的请求
                await asyncio.sleep(0.5)  # 500ms延迟

            logger.info(f"Finished fetching high/critical vulnerabilities. Total pages: {page_count}, Total vulnerabilities: {len(all_vulnerabilities)}")
            return all_vulnerabilities

        except Exception as e:
            logger.error(f"Error fetching high/critical vulnerabilities: {str(e)}")
            raise

    async def get_all_critical_vulnerabilities(self) -> List[Dict[str, Any]]:
        """获取所有严重漏洞，使用正确的API过滤参数 q=severity:4

        通过测试发现正确的参数格式是 q=severity:4，这样可以只获取Critical级别的漏洞
        预期返回约1083个严重漏洞，与AWVS界面显示的数量一致
        """
        import asyncio

        try:
            all_vulnerabilities = []
            cursor = None
            page_count = 0

            logger.info("Starting to fetch all critical vulnerabilities using q=threat:4...")

            while page_count < 15:  # 设置合理上限，1083个严重漏洞大约需要11页
                page_count += 1
                logger.info(f"Fetching critical vulnerabilities page {page_count} with cursor: {cursor[:50] if cursor else 'None'}...")

                # 添加重试机制
                max_retries = 3
                for retry in range(max_retries):
                    try:
                        # 使用正确的API过滤参数格式：q=severity:4
                        endpoint = "api/v1/vulnerabilities"
                        # 根据测试结果，正确的参数格式是 q=severity:4 (4=Critical级别)
                        params = {"q": "severity:4"}  # 只获取Critical级别漏洞
                        if cursor:
                            params['c'] = cursor

                        page_data = await self._make_request(endpoint, params=params)
                        break
                    except Exception as e:
                        if retry < max_retries - 1:
                            wait_time = (retry + 1) * 2  # 2, 4, 6秒
                            logger.warning(f"Request failed (attempt {retry + 1}/{max_retries}), retrying in {wait_time}s: {str(e)}")
                            await asyncio.sleep(wait_time)
                        else:
                            raise

                page_vulnerabilities = page_data["vulnerabilities"]
                pagination = page_data["pagination"]

                if not page_vulnerabilities:
                    logger.info("No more critical vulnerabilities found, stopping pagination")
                    break

                # 所有返回的漏洞都应该是严重级别的
                all_vulnerabilities.extend(page_vulnerabilities)
                logger.info(f"Page {page_count}: {len(page_vulnerabilities)} critical vulnerabilities")
                logger.info(f"Total critical vulnerabilities so far: {len(all_vulnerabilities)}")
                logger.info(f"API reports total critical count: {pagination.get('count', 'unknown')}")

                # 获取下一页的cursor
                cursors = pagination.get("cursors", [])
                if len(cursors) > 1 and cursors[1]:
                    cursor = cursors[1]
                    logger.info(f"Next cursor: {cursor[:50]}...")
                else:
                    logger.info("No more pages available (no next cursor)")
                    break

                # 添加延迟以避免过于频繁的请求
                await asyncio.sleep(0.5)  # 500ms延迟

            logger.info(f"Finished fetching critical vulnerabilities. Total pages: {page_count}, Total vulnerabilities: {len(all_vulnerabilities)}")
            return all_vulnerabilities

        except Exception as e:
            logger.error(f"Error fetching critical vulnerabilities: {str(e)}")
            raise

    def _normalize_vulnerabilities(self, vulnerabilities: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """标准化AWVS漏洞数据格式"""
        normalized = []

        for vuln in vulnerabilities:
            try:
                # AWVS字段映射到标准格式
                normalized_vuln = {
                    "id": str(vuln.get("vuln_id") or vuln.get("id") or vuln.get("vulnerability_id") or vuln.get("issue_id", "")),
                    "name": (vuln.get("vuln_name") or vuln.get("name") or vuln.get("title") or
                            vuln.get("vt_name") or vuln.get("issue_name") or vuln.get("vulnerability_name") or
                            vuln.get("finding_name") or "Unknown Vulnerability"),
                    "description": (vuln.get("description") or vuln.get("details") or vuln.get("summary") or
                                  vuln.get("issue_description") or ""),
                    "severity": self._map_severity(vuln.get("severity") or vuln.get("criticality") or vuln.get("risk_level", "medium")),
                    "status": self._map_status(vuln.get("status") or vuln.get("state") or vuln.get("issue_status", "open")),
                    "target_url": (vuln.get("affects_url") or vuln.get("target_url") or vuln.get("url") or
                                 vuln.get("request_url") or vuln.get("location", "")),
                    "target_name": vuln.get("target_name") or vuln.get("target") or vuln.get("host", ""),
                    "discovered_at": (vuln.get("last_seen") or vuln.get("discovered_at") or vuln.get("created_at") or
                                    vuln.get("found_date") or vuln.get("date_found", "")),
                    "cvss_score": vuln.get("cvss_score") or vuln.get("cvss") or vuln.get("score", 0),
                    "cve_id": vuln.get("cve_id") or vuln.get("cve") or vuln.get("cve_list", ""),
                    "category": (vuln.get("vt_name") or vuln.get("category") or vuln.get("type") or
                               vuln.get("vulnerability_type") or vuln.get("finding_type", "")),
                    "recommendation": (vuln.get("recommendation") or vuln.get("remediation") or
                                     vuln.get("solution") or vuln.get("fix", ""))
                }

                # 确保必要字段不为空
                if not normalized_vuln["id"]:
                    normalized_vuln["id"] = f"awvs_{len(normalized)}"

                normalized.append(normalized_vuln)

            except Exception as e:
                logger.warning(f"Failed to normalize vulnerability: {str(e)}")
                continue

        return normalized

    def _map_severity(self, severity) -> str:
        """映射AWVS严重程度到标准格式"""
        severity_map = {
            "critical": "critical",
            "high": "high",
            "medium": "medium",
            "low": "low",
            "info": "info",
            "4": "critical",
            "3": "high",
            "2": "medium",
            "1": "low",
            "0": "info"
        }
        return severity_map.get(str(severity).lower(), "medium")

    def _map_status(self, status) -> str:
        """映射AWVS状态到标准格式"""
        status_map = {
            "open": "open",
            "fixed": "fixed",
            "accepted": "accepted",
            "ignored": "ignored",
            "false_positive": "ignored"
        }
        return status_map.get(str(status).lower(), "open")

    async def test_connection(self) -> Dict[str, Any]:
        """测试AWVS API连接"""
        try:
            # 尝试获取基本信息
            endpoints_to_test = [
                "api/v1/me",
                "api/v1/info",
                "me",
                "info"
            ]

            for endpoint in endpoints_to_test:
                try:
                    logger.info(f"Testing connection with endpoint: {endpoint}")
                    data = await self._make_request(endpoint)
                    if data:
                        return {
                            "status": "connected",
                            "endpoint": endpoint,
                            "data": data
                        }
                except Exception as e:
                    logger.warning(f"Test endpoint {endpoint} failed: {str(e)}")
                    continue

            return {
                "status": "failed",
                "error": "All test endpoints failed"
            }

        except Exception as e:
            return {
                "status": "error",
                "error": str(e)
            }

    async def get_scans(self) -> List[Dict[str, Any]]:
        """获取扫描列表"""
        try:
            endpoints_to_try = [
                "api/v1/scans",
                "scans"
            ]

            for endpoint in endpoints_to_try:
                try:
                    logger.info(f"Trying to get scans from: {endpoint}")
                    data = await self._make_request(endpoint)

                    if data:
                        scans = []
                        if isinstance(data, dict) and "scans" in data:
                            scans = data["scans"]
                        elif isinstance(data, list):
                            scans = data

                        logger.info(f"Found {len(scans)} scans")
                        return scans

                except Exception as e:
                    logger.warning(f"Failed to get scans from {endpoint}: {str(e)}")
                    continue

            return []

        except Exception as e:
            logger.error(f"Error fetching scans: {str(e)}")
            return []

    async def get_vulnerabilities_from_scan(self, scan_id: str) -> List[Dict[str, Any]]:
        """从特定扫描获取漏洞"""
        try:
            endpoints_to_try = [
                f"api/v1/scans/{scan_id}/results",
                f"api/v1/scans/{scan_id}/vulnerabilities",
                f"scans/{scan_id}/results"
            ]

            for endpoint in endpoints_to_try:
                try:
                    logger.info(f"Getting vulnerabilities from scan {scan_id} via: {endpoint}")
                    data = await self._make_request(endpoint)

                    if data:
                        vulnerabilities = []
                        if isinstance(data, dict):
                            if "vulnerabilities" in data:
                                vulnerabilities = data["vulnerabilities"]
                            elif "results" in data:
                                vulnerabilities = data["results"]
                        elif isinstance(data, list):
                            vulnerabilities = data

                        if vulnerabilities:
                            logger.info(f"Found {len(vulnerabilities)} vulnerabilities in scan {scan_id}")
                            return self._normalize_vulnerabilities(vulnerabilities)

                except Exception as e:
                    logger.warning(f"Failed to get vulnerabilities from scan {scan_id} via {endpoint}: {str(e)}")
                    continue

            return []

        except Exception as e:
            logger.error(f"Error fetching vulnerabilities from scan {scan_id}: {str(e)}")
            return []

    def _parse_vulnerability(self, vuln_data: Dict[str, Any]) -> Vulnerability:
        """解析AWVS漏洞数据为标准格式"""
        try:
            # 处理严重程度映射
            severity_map = {
                "critical": SeverityLevel.CRITICAL,
                "high": SeverityLevel.HIGH,
                "medium": SeverityLevel.MEDIUM,
                "low": SeverityLevel.LOW,
                "info": SeverityLevel.INFO,
                "4": SeverityLevel.CRITICAL,
                "3": SeverityLevel.HIGH,
                "2": SeverityLevel.MEDIUM,
                "1": SeverityLevel.LOW,
                "0": SeverityLevel.INFO
            }
            
            severity_raw = str(vuln_data.get("severity", "medium")).lower()
            severity = severity_map.get(severity_raw, SeverityLevel.MEDIUM)
            
            # 处理状态映射
            status_map = {
                "open": VulnerabilityStatus.OPEN,
                "fixed": VulnerabilityStatus.FIXED,
                "accepted": VulnerabilityStatus.ACCEPTED,
                "ignored": VulnerabilityStatus.IGNORED
            }
            
            status_raw = str(vuln_data.get("status", "open")).lower()
            status = status_map.get(status_raw, VulnerabilityStatus.OPEN)
            
            # 处理时间格式 - AWVS使用last_seen字段
            discovered_at = vuln_data.get("last_seen") or vuln_data.get("discovered_at") or vuln_data.get("created_at") or datetime.now().isoformat()
            if isinstance(discovered_at, str):
                try:
                    discovered_at = datetime.fromisoformat(discovered_at.replace('Z', '+00:00'))
                except:
                    from datetime import timezone
                    discovered_at = datetime.now(timezone.utc)

            # 获取目标信息
            target_url = vuln_data.get("affects_url", "")
            target_name = ""

            # 尝试从target_url提取域名作为target_name
            if target_url:
                try:
                    from urllib.parse import urlparse
                    parsed = urlparse(target_url)
                    target_name = parsed.netloc or target_url
                except:
                    target_name = target_url

            # 如果没有target_name，使用target_description或target_id
            if not target_name:
                target_name = vuln_data.get("target_description", "") or vuln_data.get("target_id", "未知目标")

            return Vulnerability(
                id=str(vuln_data.get("vuln_id", vuln_data.get("id", ""))),
                name=vuln_data.get("vt_name", vuln_data.get("name", "Unknown Vulnerability")),
                description=vuln_data.get("description", vuln_data.get("affects_detail", "")),
                severity=severity,
                status=status,
                target_url=target_url,
                target_name=target_name,
                discovered_at=discovered_at,
                cvss_score=vuln_data.get("cvss_score", vuln_data.get("criticality")),
                cve_id=vuln_data.get("cve_id"),
                category=vuln_data.get("category", ""),
                recommendation=vuln_data.get("recommendation", ""),
                http_request=vuln_data.get("request")  # 添加HTTP请求信息
            )
        except Exception as e:
            logger.error(f"Error parsing vulnerability data: {str(e)}")
            # 返回默认漏洞对象
            return Vulnerability(
                id=str(vuln_data.get("id", "unknown")),
                name="Parse Error",
                severity=SeverityLevel.MEDIUM,
                status=VulnerabilityStatus.OPEN,
                target_url="",
                discovered_at=datetime.now(timezone.utc)
            )
    
    async def get_filtered_vulnerabilities(self, severity_levels: List[str] = None) -> List[Vulnerability]:
        """获取过滤后的漏洞列表，只获取严重漏洞"""
        if not severity_levels:
            severity_levels = ["critical"]  # 只获取严重漏洞

        # 获取所有严重漏洞
        raw_vulnerabilities = await self.get_all_critical_vulnerabilities()
        vulnerabilities = []

        for vuln_data in raw_vulnerabilities:
            vulnerability = self._parse_vulnerability(vuln_data)

            # 过滤严重程度
            if vulnerability.severity.value in severity_levels:
                vulnerabilities.append(vulnerability)

        return vulnerabilities

    async def get_vulnerability_detail(self, vulnerability_id: str) -> Optional[Vulnerability]:
        """获取单个漏洞的详细信息，包含HTTP Request等完整数据"""
        try:
            endpoint = f"api/v1/vulnerabilities/{vulnerability_id}"

            logger.info(f"Fetching vulnerability detail for ID: {vulnerability_id}")

            # 直接从AWVS API获取漏洞详情
            vuln_data = await self._make_request(endpoint)

            if vuln_data:
                # 解析漏洞数据
                vulnerability = self._parse_vulnerability(vuln_data)
                logger.info(f"Successfully fetched vulnerability detail: {vulnerability.name}")
                return vulnerability
            else:
                logger.warning(f"No data returned for vulnerability ID: {vulnerability_id}")
                return None

        except Exception as e:
            logger.error(f"Error fetching vulnerability detail {vulnerability_id}: {str(e)}")
            return None

# 创建全局实例
awvs_service = AWVSService()
