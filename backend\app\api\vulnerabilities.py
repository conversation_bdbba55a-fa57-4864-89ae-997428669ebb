from fastapi import APIRouter, HTTPException, Query
from typing import List, Optional
import logging
from app.models.vulnerability import (
    Vulnerability,
    VulnerabilityResponse,
    SeverityLevel,
    VulnerabilityStatus
)
from app.services.awvs_service import awvs_service
from app.services.database_service import database_service
from app.services.sync_service import sync_service
from app.services.scheduler_service import scheduler_service

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/vulnerabilities", tags=["vulnerabilities"])

@router.get("/", response_model=VulnerabilityResponse)
async def get_vulnerabilities(
    severity: Optional[List[str]] = Query(default=["critical"], description="漏洞严重程度过滤"),
    status: Optional[List[str]] = Query(default=["open"], description="漏洞状态过滤"),
    limit: Optional[int] = Query(default=100, ge=1, le=30000, description="返回数量限制"),
    offset: Optional[int] = Query(default=0, ge=0, description="偏移量")
):
    """
    获取漏洞列表（从本地数据库）

    - **severity**: 漏洞严重程度 (critical, high, medium, low, info)
    - **status**: 漏洞状态 (open, fixed, accepted, ignored)
    - **limit**: 返回数量限制
    - **offset**: 分页偏移量
    """
    try:
        logger.info(f"Fetching vulnerabilities from database with severity: {severity}, status: {status}")

        # 从数据库获取漏洞
        vulnerabilities = await database_service.get_vulnerabilities(
            severity_filter=severity,
            status_filter=status,
            limit=limit,
            offset=offset
        )

        # 获取统计信息
        stats = await database_service.get_vulnerability_stats()

        # 计算当前结果的统计
        critical_count = sum(1 for v in vulnerabilities if v.severity == SeverityLevel.CRITICAL)
        high_count = sum(1 for v in vulnerabilities if v.severity == SeverityLevel.HIGH)

        logger.info(f"Returning {len(vulnerabilities)} vulnerabilities from database")

        return VulnerabilityResponse(
            vulnerabilities=vulnerabilities,
            total_count=stats["total_count"],
            critical_count=critical_count,
            high_count=high_count
        )
        
    except Exception as e:
        logger.error(f"Error fetching vulnerabilities from database: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch vulnerabilities: {str(e)}")

@router.get("/{vulnerability_id}", response_model=Vulnerability)
async def get_vulnerability_detail(vulnerability_id: str):
    """
    获取特定漏洞的详细信息，包含HTTP Request等完整数据
    如果数据库中没有HTTP Request数据，会动态从AWVS获取

    - **vulnerability_id**: 漏洞ID
    """
    try:
        logger.info(f"Fetching vulnerability detail for ID: {vulnerability_id}")

        # 从数据库获取漏洞详情
        vulnerability = await database_service.get_vulnerability_by_id(vulnerability_id)

        if not vulnerability:
            raise HTTPException(status_code=404, detail="Vulnerability not found")

        # 如果数据库中没有HTTP Request数据，尝试从AWVS获取
        if not vulnerability.http_request:
            logger.info(f"No HTTP Request data in database for {vulnerability_id}, fetching from AWVS...")
            try:
                # 从AWVS获取详细信息
                awvs_detail = await awvs_service.get_vulnerability_detail(vulnerability_id)
                if awvs_detail and awvs_detail.http_request:
                    logger.info(f"Found HTTP Request data from AWVS for {vulnerability_id}")
                    # 更新数据库中的HTTP Request数据
                    await database_service.update_vulnerability_http_request(vulnerability_id, awvs_detail.http_request)
                    # 返回更新后的数据
                    vulnerability.http_request = awvs_detail.http_request
                else:
                    logger.warning(f"No HTTP Request data available from AWVS for {vulnerability_id}")
            except Exception as e:
                logger.warning(f"Failed to fetch HTTP Request from AWVS for {vulnerability_id}: {str(e)}")

        return vulnerability

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching vulnerability {vulnerability_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch vulnerability: {str(e)}")

@router.get("/stats/summary")
async def get_vulnerability_stats():
    """
    获取漏洞统计信息（从本地数据库）
    """
    try:
        # 从数据库获取统计信息
        stats = await database_service.get_vulnerability_stats()
        return stats

    except Exception as e:
        logger.error(f"Error fetching vulnerability stats from database: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch stats: {str(e)}")

@router.get("/test-connection")
async def test_awvs_connection():
    """
    测试AWVS API连接
    """
    try:
        result = await awvs_service.test_connection()
        return result
    except Exception as e:
        logger.error(f"Error testing AWVS connection: {str(e)}")
        return {
            "status": "error",
            "error": str(e)
        }

@router.post("/sync/full")
async def trigger_full_sync():
    """
    手动触发完整同步
    """
    try:
        result = await scheduler_service.trigger_manual_sync("full")
        return result
    except Exception as e:
        logger.error(f"Error triggering full sync: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to trigger full sync: {str(e)}")

@router.post("/sync/incremental")
async def trigger_incremental_sync():
    """
    手动触发增量同步
    """
    try:
        result = await scheduler_service.trigger_manual_sync("incremental")
        return result
    except Exception as e:
        logger.error(f"Error triggering incremental sync: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to trigger incremental sync: {str(e)}")

@router.get("/sync/status")
async def get_sync_status():
    """
    获取同步状态
    """
    try:
        sync_status = await sync_service.get_sync_status()
        job_status = scheduler_service.get_job_status()

        return {
            "sync_service": sync_status,
            "scheduler": job_status
        }
    except Exception as e:
        logger.error(f"Error getting sync status: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get sync status: {str(e)}")
