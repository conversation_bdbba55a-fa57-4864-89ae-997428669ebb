<template>
  <div class="vulnerability-list">
    <!-- 页面标题和过滤器 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">漏洞列表</h1>
        <p class="page-subtitle">管理和查看所有安全漏洞</p>
      </div>
      
      <div class="header-actions">
        <button @click="refreshData" class="btn btn-secondary" :disabled="isLoading">
          <RefreshCw :size="16" :class="{ 'animate-spin': isLoading }" />
          刷新
        </button>
      </div>
    </div>

    <!-- 过滤器面板 -->
    <div class="filters-panel">
      <div class="filter-group">
        <label class="filter-label">严重程度:</label>
        <div class="filter-options">
          <label class="checkbox-label">
            <input
              type="checkbox"
              value="critical"
              v-model="localFilters.severity"
              @change="applyFilters"
            />
            <span class="checkmark"></span>
            严重
          </label>
          <label class="checkbox-label">
            <input
              type="checkbox"
              value="medium"
              v-model="localFilters.severity"
              @change="applyFilters"
            />
            <span class="checkmark"></span>
            中危
          </label>
        </div>
      </div>

      <div class="filter-group">
        <label class="filter-label">状态:</label>
        <div class="filter-options">
          <label class="checkbox-label">
            <input 
              type="checkbox" 
              value="open" 
              v-model="localFilters.status"
              @change="applyFilters"
            />
            <span class="checkmark"></span>
            未修复
          </label>
          <label class="checkbox-label">
            <input 
              type="checkbox" 
              value="fixed" 
              v-model="localFilters.status"
              @change="applyFilters"
            />
            <span class="checkmark"></span>
            已修复
          </label>
        </div>
      </div>

      <div class="filter-group">
        <label class="filter-label">显示数量:</label>
        <select v-model="localFilters.limit" @change="applyFilters" class="filter-select">
          <option value="25">25</option>
          <option value="50">50</option>
          <option value="100">100</option>
        </select>
      </div>
    </div>

    <!-- 统计信息 -->
    <div class="stats-summary">
      <div class="stat-item">
        <span class="stat-label">总计:</span>
        <span class="stat-value">{{ stats.total_count }}</span>
      </div>
      <div class="stat-item critical">
        <span class="stat-label">严重:</span>
        <span class="stat-value">{{ stats.critical_count }}</span>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="isLoading" class="loading-container">
      <div class="loading-spinner"></div>
      <p>正在加载漏洞数据...</p>
    </div>

    <!-- 漏洞列表 -->
    <div v-else-if="hasVulnerabilities" class="vulnerabilities-container">
      <div class="vulnerability-list-grid">
        <div 
          v-for="vulnerability in vulnerabilities" 
          :key="vulnerability.id"
          class="vulnerability-item"
          @click="goToDetail(vulnerability.id)"
        >
          <div class="vulnerability-content">
            <div class="vulnerability-main">
              <div class="vulnerability-header">
                <h3 class="vulnerability-name">{{ vulnerability.name }}</h3>
                <div :class="['severity-badge', getSeverityClass(vulnerability.severity)]">
                  {{ getSeverityLabel(vulnerability.severity) }}
                </div>
              </div>
              
              <div class="vulnerability-meta">
                <div class="meta-row">
                  <div class="meta-item">
                    <Globe :size="14" />
                    <span class="meta-text">{{ vulnerability.target_url || '未知目标' }}</span>
                  </div>
                  <div class="meta-item">
                    <Calendar :size="14" />
                    <span class="meta-text">{{ formatDate(vulnerability.discovered_at) }}</span>
                  </div>
                </div>
                
                <div v-if="vulnerability.description" class="vulnerability-description">
                  {{ truncateText(vulnerability.description, 150) }}
                </div>
              </div>
            </div>

            <div class="vulnerability-actions">
              <div class="vulnerability-details">
                <div v-if="vulnerability.cvss_score" class="cvss-score">
                  <span class="cvss-label">CVSS:</span>
                  <span class="cvss-value">{{ vulnerability.cvss_score }}</span>
                </div>
                <div v-if="vulnerability.cve_id" class="cve-id">
                  <span class="cve-label">CVE:</span>
                  <span class="cve-value">{{ vulnerability.cve_id }}</span>
                </div>
              </div>
              
              <div class="action-buttons">
                <button class="btn btn-primary btn-sm" @click.stop="goToDetail(vulnerability.id)">
                  查看详情
                  <ArrowRight :size="14" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 分页控制 -->
      <div class="pagination-container">
        <button 
          @click="loadMore" 
          v-if="canLoadMore"
          class="btn btn-secondary"
          :disabled="isLoading"
        >
          <Plus :size="16" />
          加载更多
        </button>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else class="empty-state">
      <Shield :size="64" />
      <h3>没有找到漏洞</h3>
      <p>根据当前筛选条件，没有找到匹配的漏洞数据</p>
      <div class="empty-actions">
        <button @click="resetFilters" class="btn btn-secondary">
          重置筛选
        </button>
        <button @click="syncData" class="btn btn-primary">
          <RefreshCw :size="16" />
          同步数据
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import {
  RefreshCw,
  Globe,
  Calendar,
  ArrowRight,
  Plus,
  Shield
} from 'lucide-vue-next'
import { useVulnerabilityStore } from '@/stores/vulnerability'
import { storeToRefs } from 'pinia'

const router = useRouter()
const route = useRoute()
const vulnerabilityStore = useVulnerabilityStore()

const {
  vulnerabilities,
  stats,
  isLoading,
  hasVulnerabilities,
  filters
} = storeToRefs(vulnerabilityStore)

const {
  fetchVulnerabilities,
  updateFilters,
  getSeverityLabel,
  getSeverityClass,
  formatDate
} = vulnerabilityStore

// 刷新函数，只重新获取当前页面数据
async function refreshData() {
  await fetchVulnerabilities()
}

// 本地过滤器状态
const localFilters = ref({
  severity: ['critical', 'high'],
  status: ['open'],
  limit: 50,
  offset: 0
})

// 计算属性
const canLoadMore = computed(() => {
  return vulnerabilities.value.length >= localFilters.value.limit &&
         vulnerabilities.value.length < stats.value.total_count
})

// 方法
function applyFilters() {
  localFilters.value.offset = 0 // 重置偏移量
  updateFilters(localFilters.value)
}

function resetFilters() {
  localFilters.value = {
    severity: ['critical', 'high'],
    status: ['open'],
    limit: 50,
    offset: 0
  }
  applyFilters()
}

function loadMore() {
  localFilters.value.offset += localFilters.value.limit
  fetchVulnerabilities(localFilters.value)
}

function goToDetail(id) {
  // 在新标签页中打开漏洞详情
  const url = router.resolve(`/vulnerabilities/${id}`).href
  window.open(url, '_blank')
}

function truncateText(text, maxLength) {
  if (!text) return ''
  return text.length > maxLength ? text.substring(0, maxLength) + '...' : text
}

// 监听路由查询参数
watch(() => route.query, (newQuery) => {
  if (newQuery.severity) {
    const severityArray = Array.isArray(newQuery.severity)
      ? newQuery.severity
      : [newQuery.severity]
    localFilters.value.severity = severityArray
    applyFilters()
  }
}, { immediate: true })

onMounted(() => {
  // 初始化过滤器
  localFilters.value = { ...filters.value }

  // 如果没有数据，获取数据
  if (!hasVulnerabilities.value) {
    fetchVulnerabilities(localFilters.value)
  }
})
</script>

<style scoped>
.vulnerability-list {
  max-width: 1200px;
  margin: 0 auto;
}

/* 页面标题 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-xl);
  gap: var(--spacing-lg);
}

.header-content h1 {
  font-size: var(--font-size-2xl);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.page-subtitle {
  color: var(--text-secondary);
  font-size: var(--font-size-base);
}

/* 过滤器面板 */
.filters-panel {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-lg);
  align-items: center;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.filter-label {
  font-weight: 500;
  color: var(--text-primary);
  white-space: nowrap;
}

.filter-options {
  display: flex;
  gap: var(--spacing-md);
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  cursor: pointer;
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.checkbox-label input[type="checkbox"] {
  margin: 0;
  width: 16px;
  height: 16px;
}

.filter-select {
  padding: var(--spacing-xs) var(--spacing-sm);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  background: var(--bg-primary);
  color: var(--text-primary);
  font-size: var(--font-size-sm);
}

/* 统计信息 */
.stats-summary {
  display: flex;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-md);
  background: var(--bg-secondary);
  border-radius: var(--radius-md);
}

.stat-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.stat-label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.stat-value {
  font-weight: 600;
  color: var(--text-primary);
}

.stat-item.critical .stat-value {
  color: var(--critical-color);
}

.stat-item.high .stat-value {
  color: var(--high-color);
}

/* 漏洞列表 */
.vulnerability-list-grid {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.vulnerability-item {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  cursor: pointer;
  transition: all 0.3s ease;
}

.vulnerability-item:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
  border-color: var(--primary-color);
}

.vulnerability-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: var(--spacing-lg);
}

.vulnerability-main {
  flex: 1;
}

.vulnerability-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-sm);
  gap: var(--spacing-md);
}

.vulnerability-name {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  flex: 1;
}

.vulnerability-meta {
  margin-bottom: var(--spacing-sm);
}

.meta-row {
  display: flex;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xs);
}

.meta-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.meta-text {
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.vulnerability-description {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  line-height: 1.5;
  margin-top: var(--spacing-sm);
}

.vulnerability-actions {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: var(--spacing-sm);
}

.vulnerability-details {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
  align-items: flex-end;
}

.cvss-score, .cve-id {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: var(--font-size-sm);
}

.cvss-label, .cve-label {
  color: var(--text-secondary);
}

.cvss-value, .cve-value {
  font-weight: 600;
  color: var(--text-primary);
}

.btn-sm {
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: var(--font-size-sm);
}

/* 分页 */
.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: var(--spacing-xl);
}

/* 加载和空状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-2xl);
  color: var(--text-secondary);
}

.empty-state {
  text-align: center;
  padding: var(--spacing-2xl);
  color: var(--text-secondary);
}

.empty-state h3 {
  margin: var(--spacing-md) 0 var(--spacing-sm);
  color: var(--text-primary);
}

.empty-actions {
  display: flex;
  gap: var(--spacing-md);
  justify-content: center;
  margin-top: var(--spacing-lg);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: stretch;
  }

  .filters-panel {
    flex-direction: column;
    align-items: stretch;
  }

  .filter-group {
    flex-direction: column;
    align-items: stretch;
  }

  .filter-options {
    flex-wrap: wrap;
  }

  .stats-summary {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .vulnerability-content {
    flex-direction: column;
    gap: var(--spacing-md);
  }

  .vulnerability-actions {
    align-items: stretch;
  }

  .meta-row {
    flex-direction: column;
    gap: var(--spacing-xs);
  }

  .empty-actions {
    flex-direction: column;
  }
}
</style>
