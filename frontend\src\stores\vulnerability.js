import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { vulnerabilityAPI } from '@/services/api'

export const useVulnerabilityStore = defineStore('vulnerability', () => {
  // 状态
  const vulnerabilities = ref([])
  const stats = ref({
    total_count: 0,
    critical_count: 0,
    high_count: 0,
    medium_count: 0,
    low_count: 0,
    open_count: 0,
    fixed_count: 0
  })
  const loading = ref(false)
  const error = ref(null)
  const lastUpdated = ref(null)

  // 过滤器状态
  const filters = ref({
    severity: ['critical', 'high'],
    status: ['open'],
    limit: 50,
    offset: 0
  })

  // 计算属性
  const criticalVulnerabilities = computed(() => 
    vulnerabilities.value.filter(v => v.severity === 'critical')
  )

  const highVulnerabilities = computed(() => 
    vulnerabilities.value.filter(v => v.severity === 'high')
  )

  const hasVulnerabilities = computed(() => 
    vulnerabilities.value.length > 0
  )

  const isLoading = computed(() => loading.value)

  const hasError = computed(() => error.value !== null)

  // 操作方法
  async function fetchVulnerabilities(customFilters = {}) {
    loading.value = true
    error.value = null
    
    try {
      const params = { ...filters.value, ...customFilters }
      console.log('Fetching vulnerabilities with params:', params)
      
      const response = await vulnerabilityAPI.getVulnerabilities(params)
      
      vulnerabilities.value = response.vulnerabilities || []
      stats.value = {
        total_count: response.total_count || 0,
        critical_count: response.critical_count || 0,
        high_count: response.high_count || 0,
        medium_count: 0,
        low_count: 0,
        open_count: response.total_count || 0,
        fixed_count: 0
      }
      
      lastUpdated.value = new Date()
      console.log(`Loaded ${vulnerabilities.value.length} vulnerabilities`)
      
    } catch (err) {
      console.error('Error fetching vulnerabilities:', err)
      error.value = err.message || '获取漏洞数据失败'
      vulnerabilities.value = []
    } finally {
      loading.value = false
    }
  }

  async function fetchStats() {
    try {
      const response = await vulnerabilityAPI.getVulnerabilityStats()
      stats.value = response
    } catch (err) {
      console.error('Error fetching stats:', err)
      // 不设置错误状态，因为这不是关键功能
    }
  }

  // 手动同步数据（增量同步）
  async function syncData() {
    loading.value = true
    error.value = null

    try {
      const result = await vulnerabilityAPI.syncVulnerabilities()
      await fetchVulnerabilities()
      await fetchStats()
      return result
    } catch (err) {
      console.error('Error syncing data:', err)
      error.value = err.message || '同步数据失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  // 全量同步数据
  async function fullSyncData() {
    loading.value = true
    error.value = null

    try {
      const result = await vulnerabilityAPI.fullSyncVulnerabilities()
      await fetchVulnerabilities()
      await fetchStats()
      return result
    } catch (err) {
      console.error('Error full syncing data:', err)
      error.value = err.message || '全量同步数据失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  function updateFilters(newFilters) {
    filters.value = { ...filters.value, ...newFilters }
    fetchVulnerabilities()
  }

  function clearError() {
    error.value = null
  }

  function getVulnerabilityById(id) {
    return vulnerabilities.value.find(v => v.id === id)
  }

  // 格式化严重程度显示
  function getSeverityLabel(severity) {
    const labels = {
      critical: '严重',
      high: '高危',
      medium: '中危',
      low: '低危',
      info: '信息'
    }
    return labels[severity] || severity
  }

  // 获取严重程度颜色类
  function getSeverityClass(severity) {
    return `severity-${severity}`
  }

  // 格式化时间显示
  function formatDate(dateString) {
    if (!dateString) return '未知'
    
    try {
      const date = new Date(dateString)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    } catch {
      return '无效日期'
    }
  }

  // 获取状态显示文本
  function getStatusLabel(status) {
    const labels = {
      open: '未修复',
      fixed: '已修复',
      accepted: '已接受',
      ignored: '已忽略'
    }
    return labels[status] || status
  }

  return {
    // 状态
    vulnerabilities,
    stats,
    loading,
    error,
    lastUpdated,
    filters,
    
    // 计算属性
    criticalVulnerabilities,
    highVulnerabilities,
    hasVulnerabilities,
    isLoading,
    hasError,
    
    // 方法
    fetchVulnerabilities,
    fetchStats,
    syncData,
    fullSyncData,
    updateFilters,
    clearError,
    getVulnerabilityById,
    getSeverityLabel,
    getSeverityClass,
    formatDate,
    getStatusLabel
  }
})
